import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike, Not } from 'typeorm';
import { I18nService } from 'nestjs-i18n';
import { LoggerService } from '@common/logger/logger.service';
import { ValidationStrategy } from '@common/validation.strategy';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { Product } from './entities/product.entity';

@Injectable()
export class ProductValidationService implements ValidationStrategy<Product> {
  constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Product) private readonly productRepository: Repository<Product>,
    private readonly i18n: I18nService,
  ) {
    this.logger.setContext(ProductValidationService.name);
  }

  async validate(data: Product, action: DatabaseAction) {
    if (action === DatabaseAction.CREATE) {
      const existingProductName = await this.productRepository.findOneBy({
        name: <PERSON><PERSON>(data.name),
      });
      if (existingProductName) {
        throw new ConflictException(this.i18n.t('message.errors.already_exists', { args: { entity: `Product with name ${data.name}` } }));
      }
      // Add any creation-specific validations here
    }

    if (action === DatabaseAction.UPDATE) {
      const existingProduct = await this.productRepository.findOneBy({ id: data.id });
      if (!existingProduct) {
        throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Product' } }));
      }

      // Check if the new name conflicts with other products (excluding current product)
      const conflictingProduct = await this.productRepository.findOneBy({
        name: ILike(data.name),
        id: Not(data.id),
      });

      if (conflictingProduct) {
        throw new ConflictException(this.i18n.t('message.errors.already_exists', { args: { entity: `Product with name ${data.name}` } }));
      }
    }
  }
}
