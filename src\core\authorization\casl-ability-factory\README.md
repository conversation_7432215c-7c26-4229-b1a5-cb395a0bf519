# Enhanced CASL Ability System

This directory contains an enhanced, modular CASL ability system that provides robust authorization capabilities across multiple domains.

## Overview

The enhanced CASL ability system is designed to be:

- **Modular**: Each domain has its own ability factory
- **Flexible**: Easy to add new domains and customize permissions
- **Type-safe**: Full TypeScript support with proper type inference
- **Extensible**: Simple to extend with new functionality
- **Backward compatible**: Existing code continues to work

## Architecture

### Core Components

1. **BaseDomainAbilityFactory**: Abstract base class for all domain factories
2. **DomainAbilityFactory Interface**: Contract for domain-specific factories
3. **UnifiedAbilityFactory**: Combines all domain abilities into a unified system
4. **EnhancedAbilityGuard**: Advanced guard for permission checking
5. **Decorators**: Easy-to-use decorators for common permission checks

### Domain Factories

- **AuthorizationDomainAbilityFactory**: Handles Profile, Role, Permission entities
- **EcommerceDomainAbilityFactory**: Handles Product, Order, Cart, Review entities
- **Custom Domain Factories**: Easy to create for new domains

## Usage

### Controller-Level Usage

The CASL ability system provides decorators that work seamlessly with NestJS controllers. Apply the `@UseGuards(EnhancedAbilityGuard)` to your controller class and use permission decorators on individual methods.

#### Basic Controller Setup

```typescript
import { Controller, UseGuards } from '@nestjs/common';
import { EnhancedAbilityGuard } from './enhanced-ability.guard';
import { CheckRead, CheckCreate, CheckUpdate, CheckDelete } from './check-ability.decorator';

@Controller('products')
@UseGuards(EnhancedAbilityGuard)
export class ProductController {
  
  @Get()
  @CheckRead('Product')
  async findAll() {
    return { message: 'Products retrieved successfully' };
  }

  @Get(':id')
  @CheckRead('Product')
  async findOne(@Param('id') id: string) {
    return { message: 'Product retrieved successfully' };
  }

  @Post()
  @CheckCreate('Product')
  async create(@Body() createProductDto: any) {
    return { message: 'Product created successfully' };
  }

  @Put(':id')
  @CheckUpdate('Product')
  async update(@Param('id') id: string, @Body() updateProductDto: any) {
    return { message: 'Product updated successfully' };
  }

  @Delete(':id')
  @CheckDelete('Product')
  async remove(@Param('id') id: string) {
    return { message: 'Product deleted successfully' };
  }
}
```

#### Field-Level Permissions

Control access to specific fields within entities:

```typescript
@Controller('products')
@UseGuards(EnhancedAbilityGuard)
export class ProductController {
  
  // Allow updating only the price field
  @Put(':id/price')
  @CheckAbility({ action: UserActions.UPDATE, subject: 'Product', field: 'price' })
  async updatePrice(@Param('id') id: string, @Body() priceData: any) {
    return { message: 'Product price updated successfully' };
  }

  // Allow updating only the status field
  @Put(':id/status')
  @CheckAbility({ action: UserActions.UPDATE, subject: 'Product', field: 'status' })
  async updateStatus(@Param('id') id: string, @Body() statusData: any) {
    return { message: 'Product status updated successfully' };
  }

  // Allow updating multiple specific fields
  @Put(':id/details')
  @CheckAbility(
    { action: UserActions.UPDATE, subject: 'Product', field: 'name' },
    { action: UserActions.UPDATE, subject: 'Product', field: 'description' }
  )
  async updateDetails(@Param('id') id: string, @Body() detailsData: any) {
    return { message: 'Product details updated successfully' };
  }
}
```

#### Multiple Permission Requirements

Some operations require multiple permissions:

```typescript
@Controller('orders')
@UseGuards(EnhancedAbilityGuard)
export class OrderController {
  
  // Creating a review requires both reading the product and creating a review
  @Post(':id/review')
  @CheckAbility(
    { action: UserActions.READ, subject: 'Product' },
    { action: UserActions.CREATE, subject: 'Review' }
  )
  async createReview(@Param('id') productId: string, @Body() reviewData: any) {
    return { message: 'Review created successfully' };
  }

  // Updating order status requires both read and update permissions
  @Put(':id/status')
  @CheckAbility(
    { action: UserActions.READ, subject: 'Order' },
    { action: UserActions.UPDATE, subject: 'Order', field: 'status' }
  )
  async updateOrderStatus(@Param('id') id: string, @Body() statusData: any) {
    return { message: 'Order status updated successfully' };
  }
}
```

#### Conditional Permissions

Use conditions for user-specific data access:

```typescript
@Controller('profiles')
@UseGuards(EnhancedAbilityGuard)
export class ProfileController {
  
  // Users can only read their own profile
  @Get('me')
  @CheckAbility({ 
    action: UserActions.READ, 
    subject: 'Profile', 
    conditions: { userId: 'current' } 
  })
  async getMyProfile(@Request() req) {
    return { message: 'Profile retrieved successfully' };
  }

  // Staff can read any profile, customers can only read their own
  @Get(':id')
  @CheckRead('Profile')
  async getProfile(@Param('id') id: string, @Request() req) {
    return { message: 'Profile retrieved successfully' };
  }
}
```

### Service-Level Usage

For more granular control and business logic integration, you can use the CASL ability system directly in your services.

#### Basic Service Integration

```typescript
import { Injectable, ForbiddenException } from '@nestjs/common';
import { CaslAbilityFactoryService } from './casl-ability-factory.service';
import { UserActions } from '@common/enumerations/user_actions.enum';

@Injectable()
export class ProductService {
  constructor(
    private readonly caslAbilityFactory: CaslAbilityFactoryService,
  ) {}

  async createProduct(user: Profile, productData: CreateProductDto) {
    const ability = this.caslAbilityFactory.createForUser(user);

    if (!ability.can(UserActions.CREATE, 'Product')) {
      throw new ForbiddenException('User cannot create products');
    }

    // Proceed with product creation
    const product = await this.productRepository.create(productData);
    return product;
  }

  async updateProduct(user: Profile, productId: string, updateData: UpdateProductDto) {
    const ability = this.caslAbilityFactory.createForUser(user);
    const product = await this.productRepository.findById(productId);

    if (!ability.can(UserActions.UPDATE, 'Product')) {
      throw new ForbiddenException('User cannot update products');
    }

    // Check field-level permissions
    if (updateData.price && !ability.can(UserActions.UPDATE, 'Product', 'price')) {
      throw new ForbiddenException('User cannot update product price');
    }

    if (updateData.status && !ability.can(UserActions.UPDATE, 'Product', 'status')) {
      throw new ForbiddenException('User cannot update product status');
    }

    // Proceed with update
    const updatedProduct = await this.productRepository.update(productId, updateData);
    return updatedProduct;
  }

  async deleteProduct(user: Profile, productId: string) {
    const ability = this.caslAbilityFactory.createForUser(user);

    if (!ability.can(UserActions.DELETE, 'Product')) {
      throw new ForbiddenException('User cannot delete products');
    }

    // Proceed with deletion
    await this.productRepository.delete(productId);
    return { message: 'Product deleted successfully' };
  }
}
```

#### Advanced Service Usage with Conditions

```typescript
@Injectable()
export class OrderService {
  constructor(
    private readonly caslAbilityFactory: CaslAbilityFactoryService,
  ) {}

  async getUserOrders(user: Profile, filters: OrderFiltersDto) {
    const ability = this.caslAbilityFactory.createForUser(user);

    if (!ability.can(UserActions.READ, 'Order')) {
      throw new ForbiddenException('User cannot read orders');
    }

    // Apply user-specific conditions based on profile type
    if (user.profileType === ProfileType.CUSTOMER) {
      // Customers can only see their own orders
      filters.userId = user.id;
    }
    // Staff can see all orders (no additional filters needed)

    const orders = await this.orderRepository.findWithFilters(filters);
    return orders;
  }

  async updateOrderStatus(user: Profile, orderId: string, newStatus: string) {
    const ability = this.caslAbilityFactory.createForUser(user);
    const order = await this.orderRepository.findById(orderId);

    if (!ability.can(UserActions.UPDATE, 'Order', 'status')) {
      throw new ForbiddenException('User cannot update order status');
    }

    // Check if user can update this specific order
    if (!ability.can(UserActions.UPDATE, 'Order', order)) {
      throw new ForbiddenException('User cannot update this specific order');
    }

    // Proceed with status update
    const updatedOrder = await this.orderRepository.updateStatus(orderId, newStatus);
    return updatedOrder;
  }
}
```

#### Domain-Specific Abilities

For better performance, you can use domain-specific abilities instead of the unified ability:

```typescript
@Injectable()
export class EcommerceService {
  constructor(
    private readonly caslAbilityFactory: CaslAbilityFactoryService,
  ) {}

  async processOrder(user: Profile, orderData: CreateOrderDto) {
    // Get ecommerce-specific ability for better performance
    const ecommerceAbility = this.caslAbilityFactory.getDomainAbility(user, 'ecommerce');
    const ability = ecommerceAbility.build();

    // Check multiple permissions efficiently
    const canCreateOrder = ability.can(UserActions.CREATE, 'Order');
    const canReadProducts = ability.can(UserActions.READ, 'Product');
    const canUpdateCart = ability.can(UserActions.UPDATE, 'Cart');

    if (!canCreateOrder || !canReadProducts || !canUpdateCart) {
      throw new ForbiddenException('Insufficient permissions to process order');
    }

    // Proceed with order processing
    const order = await this.orderRepository.create(orderData);
    return order;
  }
}
```

#### Error Handling and Logging

```typescript
@Injectable()
export class SecureService {
  constructor(
    private readonly caslAbilityFactory: CaslAbilityFactoryService,
    private readonly logger: LoggerService,
  ) {}

  async performSecureOperation(user: Profile, operationData: any) {
    try {
      const ability = this.caslAbilityFactory.createForUser(user);

      if (!ability.can(UserActions.MANAGE, 'SecureResource')) {
        this.logger.warn(`Permission denied for user ${user.id} on SecureResource`);
        throw new ForbiddenException('Insufficient permissions for this operation');
      }

      // Log successful permission check
      this.logger.log(`Permission granted for user ${user.id} on SecureResource`);

      // Proceed with secure operation
      const result = await this.performOperation(operationData);
      return result;

    } catch (error) {
      if (error instanceof ForbiddenException) {
        // Re-throw permission errors
        throw error;
      }
      
      // Log unexpected errors
      this.logger.error(`Unexpected error in secure operation: ${error.message}`);
      throw new InternalServerErrorException('An unexpected error occurred');
    }
  }
}
```

## Creating Custom Domain Factories

### Step 1: Extend BaseDomainAbilityFactory

```typescript
@Injectable()
export class CustomDomainAbilityFactory extends BaseDomainAbilityFactory {
  getDomainName(): string {
    return 'custom';
  }

  getSubjects(): any[] {
    return [CustomEntity1, CustomEntity2];
  }

  getSubjectTypes(): any[] {
    return [CustomEntity1, CustomEntity2];
  }

  protected applyDomainRules(profile: Profile, ability: DomainAbility): void {
    if (profile.profileType === ProfileType.STAFF) {
      ability.can(UserActions.MANAGE, 'all');
    } else {
      // Apply customer-specific rules
      ability.can(UserActions.READ, 'CustomEntity', { userId: profile.id });
    }
  }
}
```

### Step 2: Register the Factory

```typescript
// In your module
@Module({
  providers: [
    CustomDomainAbilityFactory,
    // ... other providers
  ],
})
export class CustomModule {}

// Or register dynamically
this.caslAbilityFactory.registerDomainFactory('custom', customFactory);
```

## Available Decorators

### Basic Permission Decorators

These decorators provide simple, declarative permission checking for common CRUD operations:

- `@CheckCreate(subject, field?)` - Check create permission for a subject
- `@CheckRead(subject, field?)` - Check read permission for a subject  
- `@CheckUpdate(subject, field?)` - Check update permission for a subject
- `@CheckDelete(subject, field?)` - Check delete permission for a subject
- `@CheckManage(subject, field?)` - Check manage permission for a subject

**Usage Examples:**
```typescript
@CheckCreate('Product')           // Can create any product
@CheckCreate('Product', 'price')  // Can create product with price field
@CheckRead('Order')               // Can read any order
@CheckUpdate('Profile', 'email')  // Can update profile email field
@CheckDelete('Review')            // Can delete any review
@CheckManage('all')               // Can manage everything
```

### Domain-Specific Decorators

For better type safety and readability, use domain-specific decorators:

- `@CheckProductAbility(action, field?)` - Product-specific permission checks
- `@CheckOrderAbility(action, field?)` - Order-specific permission checks
- `@CheckProfileAbility(action, field?)` - Profile-specific permission checks
- `@CheckRoleAbility(action, field?)` - Role-specific permission checks

**Usage Examples:**
```typescript
@CheckProductAbility(UserActions.CREATE)           // Can create products
@CheckProductAbility(UserActions.UPDATE, 'price')  // Can update product prices
@CheckOrderAbility(UserActions.READ)               // Can read orders
@CheckProfileAbility(UserActions.MANAGE)           // Can manage profiles
```

### Advanced Decorator

The `@CheckAbility` decorator provides full control over permission requirements:

- `@CheckAbility(...requirements)` - Custom ability checks with complete control

**Usage Examples:**
```typescript
// Single permission check
@CheckAbility({ action: UserActions.READ, subject: 'Product' })

// Field-level permission
@CheckAbility({ action: UserActions.UPDATE, subject: 'Product', field: 'price' })

// Conditional permission
@CheckAbility({ 
  action: UserActions.READ, 
  subject: 'Order', 
  conditions: { userId: 'current' } 
})

// Multiple permission requirements
@CheckAbility(
  { action: UserActions.READ, subject: 'Product' },
  { action: UserActions.CREATE, subject: 'Review' }
)

// Complex permission with field and conditions
@CheckAbility({
  action: UserActions.UPDATE,
  subject: 'Profile',
  field: 'status',
  conditions: { profileType: 'STAFF' }
})
```

### Decorator Best Practices

1. **Use Basic Decorators** for simple CRUD operations
2. **Use Domain-Specific Decorators** for better type safety
3. **Use @CheckAbility** for complex permission requirements
4. **Combine with Guards** - Always use `@UseGuards(EnhancedAbilityGuard)`
5. **Be Specific** - Use field-level permissions when possible
6. **Use Conditions** for user-specific data access

## Permission Structure

### User Actions

```typescript
export enum UserActions {
  MANAGE = 'manage',
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
}
```

### Permission Components

1. **Action**: What the user can do (create, read, update, delete, manage)
2. **Subject**: What the user can act on (Product, Order, Profile, etc.)
3. **Field**: Optional field restriction (price, status, etc.)
4. **Conditions**: Optional conditions for the permission

## Domain-Specific Features

### Authorization Domain

- Profile management with role-based access
- Role and permission management
- Field-level restrictions for sensitive data
- Staff vs customer permission differentiation

### E-commerce Domain

- Product management with inventory controls
- Order management with status restrictions
- Cart and review management
- Field-level pricing and inventory permissions

## Advanced Features

### Cross-Domain Permissions

```typescript
const unifiedAbility = this.caslAbilityFactory.createForUser(user);

// Check permissions across multiple domains
const canManageProducts = unifiedAbility.can(UserActions.MANAGE, 'Product');
const canManageOrders = unifiedAbility.can(UserActions.MANAGE, 'Order');
```

### Domain-Specific Abilities

```typescript
const domainAbility = this.caslAbilityFactory.getDomainAbility(user, 'ecommerce');
const builtAbility = domainAbility.build();
```

### Available Domains

```typescript
const availableDomains = this.caslAbilityFactory.getAvailableDomains();
// Returns: ['authorization', 'ecommerce']
```

## Migration from Legacy System

### Before (Legacy)

```typescript
// Old way
const ability = this.caslAbilityFactory.createLegacyForUser(profile);
```

### After (Enhanced)

```typescript
// New way - same method name, enhanced functionality
const ability = this.caslAbilityFactory.createForUser(profile);

// Or use legacy method for backward compatibility
const legacyAbility = this.caslAbilityFactory.createLegacyForUser(profile);
```

## Best Practices

### General Best Practices

1. **Use Domain-Specific Decorators**: They provide better type safety and readability
2. **Implement Field-Level Permissions**: For sensitive data like pricing and status
3. **Use Conditions**: For user-specific data access
4. **Create Custom Domain Factories**: For new business domains
5. **Log Permission Checks**: For debugging and auditing
6. **Handle Errors Gracefully**: Provide meaningful error messages

### Controller-Level Best Practices

1. **Always Use Guards**: Apply `@UseGuards(EnhancedAbilityGuard)` at the controller level
2. **Be Specific with Permissions**: Use field-level permissions when possible
3. **Group Related Endpoints**: Use consistent permission patterns across related routes
4. **Handle Multiple Permissions**: Use `@CheckAbility` for operations requiring multiple permissions
5. **Use Conditional Permissions**: Implement user-specific access control

**Example of Good Controller Structure:**
```typescript
@Controller('products')
@UseGuards(EnhancedAbilityGuard)
export class ProductController {
  
  // Public read access
  @Get()
  @CheckRead('Product')
  async findAll() { /* ... */ }

  // Public read access
  @Get(':id')
  @CheckRead('Product')
  async findOne(@Param('id') id: string) { /* ... */ }

  // Staff-only creation
  @Post()
  @CheckCreate('Product')
  async create(@Body() createProductDto: CreateProductDto) { /* ... */ }

  // Field-specific updates
  @Put(':id/price')
  @CheckAbility({ action: UserActions.UPDATE, subject: 'Product', field: 'price' })
  async updatePrice(@Param('id') id: string, @Body() priceData: any) { /* ... */ }

  @Put(':id/status')
  @CheckAbility({ action: UserActions.UPDATE, subject: 'Product', field: 'status' })
  async updateStatus(@Param('id') id: string, @Body() statusData: any) { /* ... */ }

  // General updates (non-sensitive fields)
  @Put(':id')
  @CheckUpdate('Product')
  async update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto) { /* ... */ }

  // Staff-only deletion
  @Delete(':id')
  @CheckDelete('Product')
  async remove(@Param('id') id: string) { /* ... */ }
}
```

### Service-Level Best Practices

1. **Check Permissions Early**: Validate permissions at the beginning of methods
2. **Use Field-Level Validation**: Check field permissions when updating specific fields
3. **Implement Conditional Logic**: Apply business rules based on user profile type
4. **Use Domain-Specific Abilities**: For better performance in complex operations
5. **Handle Errors Consistently**: Use proper HTTP exceptions for permission errors
6. **Log Permission Activities**: Track permission checks for audit purposes

**Example of Good Service Structure:**
```typescript
@Injectable()
export class ProductService {
  constructor(
    private readonly caslAbilityFactory: CaslAbilityFactoryService,
    private readonly logger: LoggerService,
  ) {}

  async createProduct(user: Profile, productData: CreateProductDto) {
    // Check permission early
    const ability = this.caslAbilityFactory.createForUser(user);
    
    if (!ability.can(UserActions.CREATE, 'Product')) {
      this.logger.warn(`User ${user.id} attempted to create product without permission`);
      throw new ForbiddenException('Cannot create products');
    }

    // Log successful permission check
    this.logger.log(`User ${user.id} creating product: ${productData.name}`);

    // Proceed with creation
    const product = await this.productRepository.create(productData);
    return product;
  }

  async updateProduct(user: Profile, productId: string, updateData: UpdateProductDto) {
    const ability = this.caslAbilityFactory.createForUser(user);
    const product = await this.productRepository.findById(productId);

    // Check general update permission
    if (!ability.can(UserActions.UPDATE, 'Product')) {
      throw new ForbiddenException('Cannot update products');
    }

    // Check field-level permissions
    const fieldsToUpdate = Object.keys(updateData);
    for (const field of fieldsToUpdate) {
      if (!ability.can(UserActions.UPDATE, 'Product', field)) {
        throw new ForbiddenException(`Cannot update product ${field} field`);
      }
    }

    // Check if user can update this specific product
    if (!ability.can(UserActions.UPDATE, 'Product', product)) {
      throw new ForbiddenException('Cannot update this specific product');
    }

    // Log the update
    this.logger.log(`User ${user.id} updating product ${productId}: ${JSON.stringify(updateData)}`);

    // Proceed with update
    const updatedProduct = await this.productRepository.update(productId, updateData);
    return updatedProduct;
  }

  async deleteProduct(user: Profile, productId: string) {
    const ability = this.caslAbilityFactory.createForUser(user);
    const product = await this.productRepository.findById(productId);

    if (!ability.can(UserActions.DELETE, 'Product')) {
      throw new ForbiddenException('Cannot delete products');
    }

    if (!ability.can(UserActions.DELETE, 'Product', product)) {
      throw new ForbiddenException('Cannot delete this specific product');
    }

    // Log the deletion
    this.logger.log(`User ${user.id} deleting product ${productId}`);

    // Proceed with deletion
    await this.productRepository.delete(productId);
    return { message: 'Product deleted successfully' };
  }
}
```

### Performance Best Practices

1. **Use Domain-Specific Abilities**: When you only need permissions for a specific domain
2. **Cache Abilities**: For frequently accessed users (consider Redis caching)
3. **Lazy Load Domains**: Only load domain factories when needed
4. **Optimize Permission Checks**: Group multiple checks together when possible

**Performance Example:**
```typescript
@Injectable()
export class OptimizedService {
  constructor(
    private readonly caslAbilityFactory: CaslAbilityFactoryService,
  ) {}

  async performComplexOperation(user: Profile, data: any) {
    // Use domain-specific ability for better performance
    const ecommerceAbility = this.caslAbilityFactory.getDomainAbility(user, 'ecommerce');
    const ability = ecommerceAbility.build();

    // Check all permissions at once
    const permissions = {
      canCreateOrder: ability.can(UserActions.CREATE, 'Order'),
      canReadProducts: ability.can(UserActions.READ, 'Product'),
      canUpdateCart: ability.can(UserActions.UPDATE, 'Cart'),
      canDeleteCartItem: ability.can(UserActions.DELETE, 'CartItem'),
    };

    // Validate all permissions
    const missingPermissions = Object.entries(permissions)
      .filter(([_, hasPermission]) => !hasPermission)
      .map(([permission]) => permission);

    if (missingPermissions.length > 0) {
      throw new ForbiddenException(
        `Missing permissions: ${missingPermissions.join(', ')}`
      );
    }

    // Proceed with operation
    return await this.executeOperation(data);
  }
}
```

## Error Handling

The system provides comprehensive error handling:

```typescript
try {
  const canPerform = ability.can(action, subject, field, conditions);
  if (!canPerform) {
    throw new ForbiddenException('Insufficient permissions');
  }
} catch (error) {
  // Handle permission errors
  this.logger.error(`Permission check failed: ${error.message}`);
  throw new ForbiddenException('Access denied');
}
```

## Testing

### Unit Testing Domain Factories

```typescript
describe('AuthorizationDomainAbilityFactory', () => {
  let factory: AuthorizationDomainAbilityFactory;

  beforeEach(() => {
    factory = new AuthorizationDomainAbilityFactory(logger, i18n);
  });

  it('should allow staff to manage all', () => {
    const staffProfile = createStaffProfile();
    const ability = factory.createForUser(staffProfile);
    
    expect(ability.can(UserActions.MANAGE, 'all')).toBe(true);
  });
});
```

### Integration Testing

```typescript
describe('EnhancedAbilityGuard', () => {
  it('should allow authorized users', async () => {
    const request = createMockRequest(authorizedUser);
    const context = createMockContext(request);
    
    const result = await guard.canActivate(context);
    expect(result).toBe(true);
  });
});
```

## Performance Considerations

1. **Caching**: Consider caching abilities for frequently accessed users
2. **Lazy Loading**: Load domain factories only when needed
3. **Optimization**: Use specific domain abilities instead of unified when possible
4. **Monitoring**: Log performance metrics for permission checks

## Security Considerations

1. **Principle of Least Privilege**: Grant minimum necessary permissions
2. **Field-Level Security**: Restrict access to sensitive fields
3. **Audit Logging**: Log all permission checks and denials
4. **Regular Reviews**: Periodically review and update permissions
5. **Input Validation**: Validate all permission parameters

## Troubleshooting

### Common Issues

1. **Permission Denied**: Check user roles and permissions
2. **Domain Not Found**: Ensure domain factory is registered
3. **Type Errors**: Verify subject types match domain definitions
4. **Performance Issues**: Consider caching or domain-specific abilities

### Debug Mode

Enable debug logging to see detailed permission checks:

```typescript
// In your configuration
this.logger.setLogLevel('debug');
```

## Contributing

When adding new domains or features:

1. Extend `BaseDomainAbilityFactory`
2. Implement required abstract methods
3. Add appropriate decorators
4. Update documentation
5. Add tests
6. Register in the module

## Support

For issues or questions:

1. Check the usage examples
2. Review the domain factory implementations
3. Check the test files for examples
4. Consult the CASL documentation 