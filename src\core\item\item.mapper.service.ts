import { createMap, forMember, mapFrom, Mapper, MappingProfile } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CreateItemDto } from './dto/create-item.dto';
import { ItemDto } from './dto/item.dto';
import { UpdateItemDto } from './dto/update-item.dto';
import { Item } from './entities/item.entity';

@Injectable()
export class ItemMapperService extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(
        mapper,
        Item,
        ItemDto,
        forMember(
          (destination) => destination.price,
          mapFrom((source) => Number(source.price)),
        ),
        forMember(
          (destination) => destination.unit,
          mapFrom((source) => Number(source.unit)),
        ),
      );
      createMap(mapper, ItemDto, Item);
      createMap(
        mapper,
        CreateItemDto,
        Item,
        forMember(
          (destination) => destination.product,
          mapFrom((source) => ({ id: source.productId })),
        ),
      );
      createMap(mapper, UpdateItemDto, Item);
    };
  }
}
