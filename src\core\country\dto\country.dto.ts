import { AutoMap } from '@automapper/classes';
import { IsString, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { EntityDto } from '@common/base.dto';

export class CountryDto extends EntityDto {
  @AutoMap()
  @IsString()
  @ApiProperty()
  name: string;

  @AutoMap()
  @IsString()
  @ApiProperty()
  code: string;

  @AutoMap()
  @IsNumber()
  @ApiProperty()
  priceFactor: number;

  @AutoMap()
  @IsString()
  @ApiProperty()
  currencyCode: string;

  @AutoMap()
  @IsString()
  @ApiProperty()
  currencySymbol: string;
}
