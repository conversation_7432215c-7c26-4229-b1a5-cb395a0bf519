import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';

import { LoggerModule } from '@common/logger/logger.module';
import { CloudinaryService } from '@core/storage/cloudinary/cloudinary.service';
import { JwtAuthGuard } from '@core/authentication/guard/jwt-auth.guard';

import { NotificationModule } from '@core/notification/notification.module';
import { IntegrationModule } from '@core/integration/integration.module';
import { AuthenticationModule } from '@core/authentication/authentication.module';
import { AuthorizationModule } from '@core/authorization/authorization.module';
import { ProfileModule } from '@core/profile/profile.module';
import { StaffModule } from '@core/staff/staff.module';
import { CustomerModule } from '@core/customer/customer.module';
import { OrderModule } from '@core/order/order.module';
import { OrderItemModule } from '@core/order_item/order_item.module';
import { ProductModule } from '@core/product/product.module';
import { ReviewModule } from '@core/review/review.module';
import { CartItemModule } from '@core/cart_item/cart_item.module';
import { CartModule } from '@core/cart/cart.module';
import { ShippingModule } from '@core/shipping/shipping.module';
import { StoreModule } from '@core/store/store.module';
import { ItemModule } from '@core/item/item.module';
import { CountryModule } from '@core/country/country.module';
import { LocationModule } from './location/location.module';


@Module({
  providers: [
    {
      provide: 'StorageService',
      useClass: CloudinaryService,
    },
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    JwtService,
  ],
  imports: [
    LoggerModule,
    NotificationModule,
    IntegrationModule,
    AuthenticationModule,
    AuthorizationModule,
    ProfileModule,
    StaffModule,
    CustomerModule,
    OrderModule,
    OrderItemModule,
    ProductModule,
    ReviewModule,
    CartItemModule,
    CartModule,
    ShippingModule,
    StoreModule,
    ItemModule,
    CountryModule,
    LocationModule,
  ],
})
export class CoreModule {}
