import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { LoggerService } from '@common/logger/logger.service';
import { PaginationDto } from '@common/pagination.dto';
import { CoreUtils } from '@common/utils/core.utils';
import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, ParseIntPipe, Patch, Post, Query } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { I18nService } from 'nestjs-i18n';
import { Pagination } from 'nestjs-typeorm-paginate';
import { ILike, Like } from 'typeorm';
import { ActivateProductDto } from './dto/activate-product.dto';
import { CreateProductDto } from './dto/create-product.dto';
import { DeactivateProductDto } from './dto/deactivate-product.dto';
import { ProductDto } from './dto/product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { Product } from './entities/product.entity';
import { ProductService } from './product.service';

@ApiTags('Product Endpoints')
@Controller({
  path: 'product',
  version: '1',
})
export class ProductController {
  constructor(
    private readonly productService: ProductService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(ProductController.name);
  }

  @ApiOperation({ summary: 'Get products in table format' })
  @HttpCode(HttpStatus.OK)
  @Get()
  async table(@Query() paginationDto: PaginationDto, @CurrentRoute() route: string) {
    return CoreUtils.handleRequest(async () => {
      let { page = 1, limit = 10, search, filter } = paginationDto;

      limit = limit > 100 ? 100 : limit; // limit the pagination to 100
      const where = {};
      if (search) {
        where['name'] = ILike(`%${search}%`);
      }
      if (filter) {
        where['status'] = filter;
      }

      const pagination = await this.productService.paginate(
        {
          page,
          limit,
          route,
        },
        where,
      );

      const result = await this.classMapper.mapArrayAsync(pagination.items, Product, ProductDto);

      return {
        message: this.i18n.t('message.success.retrieved', { args: { entity: 'Products' } }),
        data: new Pagination(result, pagination.meta, pagination.links),
      };
    });
  }

  @ApiOperation({ summary: 'Get product details' })
  @Get('/:id')
  async getProductDetails(@Param('id', ParseIntPipe) id: number) {
    return CoreUtils.handleRequest(async () => {
      const existingProduct: Product = await this.productService.findByPk(id);
      const data = await this.classMapper.mapAsync(existingProduct, Product, ProductDto);
      return {
        message: this.i18n.t('message.success.retrieved', { args: { entity: 'Product' } }),
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Create product' })
  @ApiBody({ type: CreateProductDto })
  @Post('new')
  async createProduct(@Body() createProductDto: CreateProductDto) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.classMapper.mapAsync(createProductDto, CreateProductDto, Product);
      await this.productService.create(data);
      return { message: this.i18n.t('message.success.created', { args: { entity: 'Product' } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Update product' })
  @ApiBody({ type: UpdateProductDto })
  @Patch(':id')
  async updateProduct(@Param('id', ParseIntPipe) id: number, @Body() updateProductDto: UpdateProductDto) {
    return CoreUtils.handleRequest(async () => {
      const product = await this.productService.findByPk(id);
      await this.classMapper.mutateAsync(updateProductDto, product, ProductDto, Product);
      await this.productService.modify(id, product);
      return { message: this.i18n.t('message.success.updated', { args: { entity: 'Product' } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Activate products' })
  @ApiBody({ type: ActivateProductDto })
  @Patch('activate')
  async activateProducts(@Body() body: ActivateProductDto) {
    return CoreUtils.handleRequest(async () => {
      await this.productService.activate(body.ids);
      return { message: this.i18n.t('message.success.activated', { args: { entity: `Product${body.ids.length > 1 ? 's' : ''}` } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Deactivate products' })
  @ApiBody({ type: DeactivateProductDto })
  @Post('deactivate')
  async deactivateProducts(@Body() body: DeactivateProductDto) {
    return CoreUtils.handleRequest(async () => {
      await this.productService.deactivate(body.ids);
      return { message: this.i18n.t('message.success.deactivated', { args: { entity: `Product${body.ids.length > 1 ? 's' : ''}` } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Delete product' })
  @Delete(':id')
  async deleteProduct(@Param('id', ParseIntPipe) id: number) {
    return CoreUtils.handleRequest(async () => {
      await this.productService.remove(id);
      return { message: this.i18n.t('message.success.deleted', { args: { entity: 'Product' } }), data: null };
    });
  }
}
