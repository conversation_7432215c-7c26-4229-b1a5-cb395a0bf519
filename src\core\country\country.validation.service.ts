import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { I18nService } from 'nestjs-i18n';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { ValidationStrategy } from '@common/validation.strategy';
import { Country } from '@core/country/entities/country.entity';

@Injectable()
export class CountryValidationService
  implements ValidationStrategy<Country>
{
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectRepository(Country)
    private readonly countryRepo: Repository<Country>,
  ) {
    this.logger.setContext(CountryValidationService.name);
  }

  async validate(data: Country, action: DatabaseAction) {
    const existingCountry = await this.countryRepo.findOne({
      where: { id: data.id },
    });

    if (DatabaseAction.CREATE === action && existingCountry) {
      throw new ConflictException(
        this.i18n.t('message.errors.already_exists', {
          args: { entity: 'Country' },
        }),
      );
    }

    if (DatabaseAction.UPDATE === action && !existingCountry) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', {args: { entity: 'Country' }}));
    }
  }
}
