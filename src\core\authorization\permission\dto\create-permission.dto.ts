import { RoleDto } from '../../role/dto/role.dto';
import { ApiProperty } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreatePermissionDto {
  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'The name of the permission',
    example: 'Admin',
    type: String,
  })
  name: string;

  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'The description of the permission',
    type: String,
  })
  description?: string;

  @AutoMap(() => RoleDto)
  @ApiProperty({
    description: 'List of roles associated with the permission',
    type: RoleDto,
    isArray: true,
    example: [{ id: 1, name: 'CREATE_USER', description: 'Role to create a user' }],
    required: false,
  })
  roles?: Array<RoleDto>;
}
