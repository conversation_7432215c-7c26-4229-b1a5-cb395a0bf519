import { IsEmail, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SendEmailDto {
    @ApiProperty({
        name: 'recipient',
        description: 'The recipient email address',
        example: '<EMAIL>',
        required: true,
    })
    @IsEmail({}, { each: true })
    recipient: string;

    @ApiProperty({
        name: 'cc',
        description: 'The cc email address',
        example: '<EMAIL>,<EMAIL>,<EMAIL>',
        required: false,
    })
    @IsString()
    cc: string;

    @ApiProperty({
        name: 'subject',
        description: 'The subject of the email',
        example: 'Welcome to Green Pastures',
        required: true,
    })
    @IsString()
    subject: string;

    @ApiProperty({
        name: 'message',
        description: 'The message of the email',
        example: 'This is a welcome email from Green Pastures',
        required: true,
    })
    @IsString()
    message: string;

    @ApiProperty({
        name: 'html',
        description: 'The html content of the email',
        example: '<h1>Welcome to Green Pastures</h1><p>This is a welcome email from Green Pastures</p>',
        required: false,
    })
    @IsString()
    @IsOptional()
    html:String;
}
