import { config } from 'dotenv';
import {
  GoogleRecaptchaModuleOptions,
  GoogleRecaptchaNetwork,
} from '@nestlab/google-recaptcha';

config();

export const recaptchaConfig = (): GoogleRecaptchaModuleOptions => ({
  secretKey: process.env.GOOGLE_RECAPTCHA_SECRET_KEY,
  response: (req) => req.body.recaptcha,
  skipIf: false, // process.env.NODE_ENV !== 'production',
  network: GoogleRecaptchaNetwork.Recaptcha,
});
