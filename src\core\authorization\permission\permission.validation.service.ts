import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { I18nService } from 'nestjs-i18n';
import { InjectRepository } from '@nestjs/typeorm';
import { Permission } from './entities/permission.entity';
import { Repository } from 'typeorm';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { ValidationStrategy } from '@common/validation.strategy';

@Injectable()
export class PermissionValidationService
  implements ValidationStrategy<Permission>
{
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectRepository(Permission)
    private readonly permissionRepo: Repository<Permission>,
  ) {
    this.logger.setContext(PermissionValidationService.name);
  }

  async validate(data: Permission, action: DatabaseAction) {
    const existingPermission = await this.permissionRepo.findOne({
      where: { id: data.id },
    });

    if (DatabaseAction.CREATE === action && existingPermission) {
      throw new ConflictException(
        this.i18n.t('message.errors.already_exists', {
          args: { entity: 'Permission' },
        }),
      );
    }

    if (DatabaseAction.UPDATE === action && !existingPermission) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', {
        args: { entity: 'Permission'},
      }));
    }
  }
}
