import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { JwtPayload } from 'jsonwebtoken';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { Profile } from '@core/profile/entities/profile.entity';
import { ProfileService } from '@core/profile/profile.service';

interface JwtPayloadWithType extends JwtPayload {
  type?: string;
}

@Injectable()
export class AccessTokenStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    private readonly configService: ConfigService,
    private readonly profileService: ProfileService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.get<string>('keys.secret'),
    });
  }

  async validate(payload: JwtPayloadWithType): Promise<Profile> {
    const { email, type } = payload;

    // Validate token type
    if (type !== 'access') {
      throw new Error('Invalid token type');
    }

    const profile = await this.profileService.findProfileByEmail(email);
    await this.profileService.checkProfileEligibility(profile);

    return profile;
  }
}
