import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { LoggerService } from '@common/logger/logger.service';
import { CoreUtils } from '@common/utils/core.utils';
import { Body, Controller, DefaultValuePipe, Get, Param, ParseIntPipe, Patch, Query } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { I18nService } from 'nestjs-i18n';
import { CustomerService } from './customer.service';
import { ActivateCustomerDto } from './dto/activate-customer.dto';
import { CustomerDto } from './dto/customer.dto';
import { DeactivateCustomerDto } from './dto/deactivate-customer.dto';
import { Customer } from './entities/customer.entity';
import { Pagination } from 'nestjs-typeorm-paginate';

@ApiTags('Customer Management Endpoints')
@Controller({
  path: 'customer',
  version: '1',
})
export class CustomerController {
  constructor(
    private readonly customerService: CustomerService,
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(CustomerController.name);
  }

  @ApiOperation({ summary: 'Get all customers' })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @Get()
  async getAllCustomers(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @CurrentRoute() route: string,
  ) {
    return CoreUtils.handleRequest(async () => {
      const { items, meta, links } = await this.customerService.findAllCustomers({ page, limit, search, filter }, route);
      const customers = await this.classMapper.mapArrayAsync(items, Customer, CustomerDto);
      const data = new Pagination(customers, meta, links);
      return { message: this.i18n.t('success.retrieved', { args: { entity: 'Customers' } }), data };
    });
  }

  @ApiOperation({ summary: 'Get one customer' })
  @Get(':id')
  async getOneCustomer(@Param('id') id: number) {
    return CoreUtils.handleRequest(async () => {
      const customer = await this.customerService.findByPk(id);
      const data = await this.classMapper.mapAsync(customer, Customer, CustomerDto);
      return { message: this.i18n.t('success.retrieved', { args: { entity: 'Customer' } }), data };
    });
  }

  @ApiOperation({ summary: 'Activate customers' })
  @ApiBody({ type: ActivateCustomerDto })
  @Patch('activate')
  async activateCustomers(@Body() body: ActivateCustomerDto) {
    return CoreUtils.handleRequest(async () => {
      await this.customerService.activate(body.ids);
      return { message: this.i18n.t('success.activated', { args: { entity: `Customer${body.ids.length > 1 ? 's' : ''}` } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Deactivate customers' })
  @ApiBody({ type: DeactivateCustomerDto })
  @Patch('deactivate')
  async deactivateCustomers(@Body() body: DeactivateCustomerDto) {
    return CoreUtils.handleRequest(async () => {
      await this.customerService.deactivate(body.ids);
      return { message: this.i18n.t('success.deactivated', { args: { entity: `Customer${body.ids.length > 1 ? 's' : ''}` } }), data: null };
    });
  }
}
