export const rateLimitConfig = {
  // Global rate limiting
  global: {
    ttl: 60000, // 1 minute
    limit: 100, // 100 requests per minute
  },
  
  // Authentication endpoints - stricter limits for security
  auth: {
    login: {
      ttl: 60000, // 1 minute
      limit: 5, // 5 login attempts per minute
    },
    googleOAuth: {
      ttl: 60000, // 1 minute
      limit: 5, // 5 Google OAuth attempts per minute
    },
    forgotPassword: {
      ttl: 300000, // 5 minutes
      limit: 3, // 3 password reset requests per 5 minutes
    },
    resendOtp: {
      ttl: 300000, // 5 minutes
      limit: 3, // 3 OTP resend requests per 5 minutes
    },
    signup: {
      ttl: 300000, // 5 minutes
      limit: 3, // 3 signup attempts per 5 minutes
    },
  },
  
  // API endpoints - standard limits
  api: {
    default: {
      ttl: 60000, // 1 minute
      limit: 60, // 60 requests per minute
    },
    read: {
      ttl: 60000, // 1 minute
      limit: 100, // 100 read requests per minute
    },
    write: {
      ttl: 60000, // 1 minute
      limit: 30, // 30 write requests per minute
    },
  },
};
