import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { Role } from './entities/role.entity';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { LoggerService } from '@common/logger/logger.service';
import { Repository } from 'typeorm';
import { I18nService } from 'nestjs-i18n';
import { InjectRepository } from '@nestjs/typeorm';
import { ValidationStrategy } from '@common/validation.strategy';

@Injectable()
export class RoleValidationService implements ValidationStrategy<Role> {
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectRepository(Role) private readonly roleRepo: Repository<Role>,
  ) {
    this.logger.setContext(RoleValidationService.name);
  }

  async validate(data: Role, action: DatabaseAction) {
    const existingRole: Role = await this.roleRepo.findOne({
      where: { name: data.name },
    });

    if (DatabaseAction.CREATE === action && existingRole) {
      throw new ConflictException(
        this.i18n.t('message.errors.role_already_exists', {
          args: { entity: 'Role' },
        }),
      );
    }

    if (DatabaseAction.UPDATE === action && !existingRole) {
      throw new NotFoundException(this.i18n.t('message.errors.role_not_found'));
    }
  }
}
