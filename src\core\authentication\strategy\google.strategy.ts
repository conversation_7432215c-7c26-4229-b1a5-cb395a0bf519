import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { LoggerService } from '@common/logger/logger.service';

export interface IGoogleProfile {
  id: string | number;
  firstName?: string;
  lastName?: string;
  email: string;
  picture: string;
}

/*
    Follow this tutorial to get required credentials:
    https://dev.to/imichaelowolabi/how-to-implement-login-with-google-in-nest-js-2aoa

	Follow this tutorial for learning more:
	https://blog.logrocket.com/implement-secure-single-sign-on-nestjs-google/
*/

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  private readonly logger = new LoggerService(GoogleStrategy.name);
  
  constructor(
    private configService: ConfigService,
  ) {
    // Validate required OAuth credentials
    const clientId = configService.get<string>('oauth.google.clientId');
    const clientSecret = configService.get<string>('oauth.google.secret');
    
    if (!clientId || !clientSecret) {
      throw new Error('Google OAuth credentials not configured. Please set oauth.google.clientId and oauth.google.secret');
    }

    super({
      clientID: clientId,
      clientSecret: clientSecret,
      callbackURL: configService.get<string>('oauth.google.callbackUrl') || 
        `http://localhost:${configService.get<string>('port')}/auth/google/callback`,
      scope: ['email', 'profile'],
    });
  }

  async validate(
    _accessToken: string,
    _refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    try {
      this.logger.log(`Google OAuth validation for profile: ${profile.id}`);

      const { id, name, emails, photos } = profile;

      // Validate required profile data
      if (!emails || !emails[0]?.value) {
        return done(new Error('Email is required from Google OAuth profile'), null);
      }

      // Extract essential profile information with fallbacks
      const googleProfile: IGoogleProfile = {
        id: id,
        email: emails[0].value,
        firstName: name?.givenName || '',
        lastName: name?.familyName || '',
        picture: photos?.[0]?.value || '',
      };

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(googleProfile.email)) {
        return done(new Error('Invalid email format from Google OAuth'), null);
      }

      done(null, googleProfile);
    } catch (error) {
      this.logger.error(`Google OAuth validation failed: ${error.message}`, error.stack);
      done(error, null);
    }
  }
}
