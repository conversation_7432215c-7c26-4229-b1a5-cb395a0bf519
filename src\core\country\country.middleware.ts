import { Injectable, NestMiddleware } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { CountryService } from '@core/country/country.service';
import { RedisService } from '@database/redis/redis.service';
import { NextFunction, Request, Response } from 'express';
import { Country } from '@core/country/entities/country.entity';

/**
 * TenantMiddleware - Provides country context without strict enforcement
 *
 * This middleware:
 * - Provides country context when country codes are available
 * - Skips country lookup gracefully when country codes are missing
 * - Attaches country information to request for optional use
 * - Never blocks requests - purely informational
 *
 * It's designed to be flexible - applications can use country context when available
 * but aren't required to provide country codes for every request.
 */
@Injectable()
export class CountryMiddleware implements NestMiddleware {
  constructor(
    private readonly logger: LoggerService,
    private readonly countryService: CountryService,
    private readonly redisService: RedisService
  ){
    this.logger.setContext(CountryMiddleware.name)
  }

  async use(req: Request, res: Response, next: NextFunction) {
    const countryCode = req.headers['x-country-code'] as string;
    let country: Country;

    if (countryCode) {
      // Check Redis cache first
      if (await this.redisService.isKeyExists(`tenant:${countryCode}`)) {
        country = await this.redisService.getCacheKey(`tenant:${countryCode}`);
      }

      // If not in cache, fetch from database
      if (!country) {
        country = await this.countryService.findByCountryCode(countryCode);
      }

      if (country) {
        req['country'] = country;
        this.logger.debug(`Tenant found for country: ${countryCode}`);
      } else {
        this.logger.warn(`No country found for: ${countryCode}`);
      }
    } else {
      this.logger.debug('No country code provided, skipping country lookup');
    }

    next();
  }
}
