import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateProductDto {
  @ApiProperty({
    description: 'Name of the product',
    example: 'Milk',
    type: String,
  })
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Description of the product',
    example: 'Fresh milk from local farms',
    type: String,
    required: false,
  })
  @AutoMap()
  @IsString()
  @IsOptional()
  description?: string;
}
