import { <PERSON>, Get, Head } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('System Endpoints')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Head()
  healthCheck(): void {
    // Handles HEAD requests for health checks from Render
    // Returns 200 OK by default
  }
}
