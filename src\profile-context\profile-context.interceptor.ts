import { AbstractEntity } from '@common/base.entity';
import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { Observable } from 'rxjs';

export interface UserProfile {
  id?: number;
  firstName?: string;
  lastName?: string;
  email?: string;
}

export interface UserContext {
  userName: string;
  userId: number;
}

@Injectable()
export class ProfileContextInterceptor implements NestInterceptor {
  private readonly logger = new LoggerService(ProfileContextInterceptor.name);
  private readonly DEFAULT_USER_NAME = 'SYSTEM';
  private readonly DEFAULT_USER_ID = 1;

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    try {
      const request = context.switchToHttp().getRequest();
      const userContext = this.extractUserContext(request);

      this.setUserContext(userContext);
      this.logUserContext(userContext);

      return next.handle();
    } catch (error) {
      this.handleError(error);
      return next.handle();
    }
  }

  private extractUserContext(request: any): UserContext {
    const profile = request.user as UserProfile | undefined;

    if (!profile) {
      this.logger.warn('No user profile found in request, using SYSTEM context');
      return this.createDefaultUserContext();
    }

    const userId = this.extractUserId(profile);
    const userName = this.buildUserName(profile, userId);

    return { userName, userId };
  }

  private extractUserId(profile: UserProfile): number {
    return profile.id || this.DEFAULT_USER_ID;
  }

  private buildUserName(profile: UserProfile, userId: number): string {
    if (profile.firstName && profile.lastName) {
      return `${profile.firstName} ${profile.lastName}`;
    }

    if (profile.firstName) {
      return profile.firstName;
    }

    if (profile.lastName) {
      return profile.lastName;
    }

    if (profile.email) {
      return profile.email;
    }

    return `User-${userId}`;
  }

  private createDefaultUserContext(): UserContext {
    return {
      userName: this.DEFAULT_USER_NAME,
      userId: this.DEFAULT_USER_ID
    };
  }

  private setUserContext(userContext: UserContext): void {
    AbstractEntity.setCurrentUser(userContext.userName);
  }

  private logUserContext(userContext: UserContext): void {
    this.logger.log(
      `Setting user context - User: ${userContext.userName}, ID: ${userContext.userId}`
    );
  }

  private handleError(error: unknown): void {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;

    this.logger.error(
      `Error in ProfileContextInterceptor: ${errorMessage}`,
      errorStack
    );

    // Fallback to SYSTEM context if there's an error
    AbstractEntity.setCurrentUser(this.DEFAULT_USER_NAME);
  }
}
