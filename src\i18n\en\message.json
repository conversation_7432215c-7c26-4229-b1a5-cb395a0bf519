{"errors": {"not_found": "{entity} not found", "already_exists": "{entity} already exists", "ability": "You can't delete {entity}", "login": "<PERSON><PERSON> failed", "signup": "Signup failed", "invalid_credentials": "Invalid {field}", "invalid_content": "Invalid content", "not_verified": "{entity} has not been verified", "suspended": "{entity} has been suspended", "banned": "{entity} has been banned", "deactivated": "{entity} has been deactivated", "verify_token_fail": "Could not verify token", "recover_password_fail": "Could not send reset password link", "reset_password_fail": "Could not reset password", "token_expired": "<PERSON><PERSON> has expired. Please try again", "recaptcha_failed": "<PERSON><PERSON><PERSON><PERSON> failed", "register_fail": "{fieldOne} or {fieldTwo} is not registered to an account", "account_inactive": "Account is inactive", "password_incorrect": "Password is incorrect. {attemptsRemaining} attempt(s) remaining.", "password_mismatch": "Password mismatch", "already_verified": "{entity} is already verified", "configuration_not_found": "{party} configuration not found", "otp_expired": "OTP has expired", "otp_send_fail": "Could not send OTP", "otp_verify_fail": "Could not verify OTP", "profile_suspended": "Profile is suspended", "profile_banned": "Profile is banned", "profile_deactivated": "Profile is deactivated", "invalid_account_type": "Invalid account type", "validation_error": "{entity} validation failed", "seed_error": "{entity} seed failed", "seed_process": "Seed process failed", "email_taken": "Email is already taken", "role_already_exists": "Role already exists", "role_not_found": "Role not found", "cannot_delete": "You cannot delete {entity}", "cannot_delete_critical": "You cannot delete critical {entity}", "restricted_field": "Access to field '{field}' is restricted", "google_token_required": "Google access token is required and cannot be empty", "google_token_invalid": "Invalid or expired Google access token", "google_token_format": "Invalid Google access token format", "google_rate_limit": "Google API rate limit exceeded. Please try again later", "google_verification_failed": "Failed to verify Google token. Please try again", "google_authentication_failed": "Google authentication failed. Please try again", "google_email_required": "Valid email is required from Google OAuth", "google_email_invalid": "Invalid email format from Google OAuth", "google_user_id_required": "User ID is required from Google OAuth", "google_picture_url_invalid": "Invalid picture URL from Google OAuth"}, "success": {"created": "{entity} created successfully", "retrieved": "{entity} retrieved successfully", "updated": "{entity} updated successfully", "deleted": "{entity} deleted successfully", "activated": "{entity} activated successfully", "deactivated": "{entity} deactivated successfully", "ability": "You can delete {entity}", "login": "Login successful", "sign_up": "Sign Up successful. Please verify your account with the OTP sent to your mail.", "reset_password": "Password reset successfully", "account_verified": "Account verified successfully", "verification_otp_resent": "Verification OTP resent successfully", "password_reset_otp_sent": "Password reset OTP sent successfully", "seeded": "{entity} seeded successfully", "seed_process": "Seed process successful", "registration": "Registration successful", "logout": "Logout successful", "logout_all_devices": "Logged out from all devices", "sessions_retrieved": "Sessions retrieved successfully", "google_signup": "Google signup successful", "google_signin": "Google signin successful", "tokens_refreshed": "Tokens refreshed successfully"}}