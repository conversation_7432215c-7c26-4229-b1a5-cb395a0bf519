import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { LoggerService } from '@common/logger/logger.service';
import { PaginationDto } from '@common/pagination.dto';
import { CoreUtils } from '@common/utils/core.utils';
import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, ParseIntPipe, Patch, Post, Query } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { I18nService } from 'nestjs-i18n';
import { Pagination } from 'nestjs-typeorm-paginate';
import { ILike } from 'typeorm';
import { ActivateItemDto } from './dto/activate-item.dto';
import { CreateItemDto } from './dto/create-item.dto';
import { DeactivateItemDto } from './dto/deactivate-item.dto';
import { ItemDto } from './dto/item.dto';
import { UpdateItemDto } from './dto/update-item.dto';
import { Item } from './entities/item.entity';
import { ItemService } from './item.service';

@ApiTags('Item Endpoints')
@Controller({
  path: 'item',
  version: '1',
})
export class ItemController {
  constructor(
    private readonly itemService: ItemService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(ItemController.name);
  }

  @ApiOperation({ summary: 'Get items in table format' })
  @HttpCode(HttpStatus.OK)
  @Get()
  async table(@Query() paginationDto: PaginationDto, @CurrentRoute() route: string) {
    return CoreUtils.handleRequest(async () => {
      let { page = 1, limit = 10, search, filter } = paginationDto;

      limit = limit > 100 ? 100 : limit; // limit the pagination to 100
      const where = {};
      if (search) {
        where['name'] = ILike(`%${search}%`);
      }
      if (filter) {
        where['status'] = filter;
      }

      const pagination = await this.itemService.paginate(
        {
          page,
          limit,
          route,
        },
        where,
      );

      const result = await this.classMapper.mapArrayAsync(pagination.items, Item, ItemDto);

      return {
        message: this.i18n.t('message.success.retrieved', { args: { entity: 'Items' } }),
        data: new Pagination(result, pagination.meta, pagination.links),
      };
    });
  }

  @ApiOperation({ summary: 'Get item details' })
  @Get(':id')
  async getItemDetails(@Param('id', ParseIntPipe) id: number) {
    return CoreUtils.handleRequest(async () => {
      const existingItem: Item = await this.itemService.findByPk(id);
      const data = await this.classMapper.mapAsync(existingItem, Item, ItemDto);
      return {
        message: this.i18n.t('message.success.retrieved', { args: { entity: 'Item' } }),
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Create item' })
  @ApiBody({ type: CreateItemDto })
  @Post('new')
  async createItem(@Body() createItemDto: CreateItemDto) {
    return CoreUtils.handleRequest(async () => {
      const item = await this.classMapper.mapAsync(createItemDto, CreateItemDto, Item);
      await this.itemService.create(item);
      return { message: this.i18n.t('message.success.created', { args: { entity: 'Item' } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Update item' })
  @ApiBody({ type: UpdateItemDto })
  @Patch(':id')
  async updateItem(@Param('id', ParseIntPipe) id: number, @Body() updateItemDto: UpdateItemDto) {
    return CoreUtils.handleRequest(async () => {
      const item = await this.itemService.findByPk(id);
      await this.classMapper.mutateAsync(updateItemDto, item, ItemDto, Item);
      await this.itemService.modify(id, item);
      return { message: this.i18n.t('message.success.updated', { args: { entity: 'Item' } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Activate items' })
  @ApiBody({ type: ActivateItemDto })
  @Patch('activate')
  async activateItems(@Body() body: ActivateItemDto) {
    return CoreUtils.handleRequest(async () => {
      await this.itemService.activate(body.ids);
      return { message: this.i18n.t('success.activated', { args: { entity: `Item${body.ids.length > 1 ? 's' : ''}` } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Deactivate items' })
  @ApiBody({ type: DeactivateItemDto })
  @Patch('deactivate')
  async deactivateItems(@Body() body: DeactivateItemDto) {
    return CoreUtils.handleRequest(async () => {
      await this.itemService.deactivate(body.ids);
      return { message: this.i18n.t('success.deactivated', { args: { entity: `Item${body.ids.length > 1 ? 's' : ''}` } }), data: null };
    });
  }

  @ApiOperation({ summary: 'Delete item' })
  @Delete(':id')
  async deleteItem(@Param('id', ParseIntPipe) id: number) {
    return CoreUtils.handleRequest(async () => {
      await this.itemService.remove(id);
      return { message: this.i18n.t('success.deleted', { args: { entity: 'Item' } }), data: null };
    });
  }
}
