import { Injectable } from '@nestjs/common';
import { AbilityBuilder, AbilityClass, ExtractSubjectType, InferSubjects, PureAbility } from '@casl/ability';
import { UserActions } from '@common/enumerations/user_actions.enum';
import { Profile } from '@core/profile/entities/profile.entity';
import { Role } from '@core/authorization/role/entities/role.entity';
import { LoggerService } from '@common/logger/logger.service';
import { I18nService } from 'nestjs-i18n';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { UnifiedAbility, UnifiedAbilityFactory } from './unified-ability.factory';

// Legacy type definitions for backward compatibility
export type Subjects = InferSubjects<typeof Profile | typeof Role> | 'all';
export type AppAbility = PureAbility<[UserActions, Subjects]>;

/**
 * Main CASL ability factory service that provides a unified interface for creating abilities.
 *
 * This service acts as a facade for the enhanced CASL ability system, providing both
 * the new unified ability functionality and backward compatibility with the legacy system.
 * It serves as the primary entry point for creating abilities in the application.
 *
 * Key Features:
 * - Unified ability creation across all domains
 * - Domain-specific ability access
 * - Backward compatibility with legacy system
 * - Comprehensive permission checking
 * - Domain registration and management
 *
 * Architecture:
 * - Delegates to UnifiedAbilityFactory for new functionality
 * - Maintains legacy createLegacyForUser method for backward compatibility
 * - Provides domain-specific access methods
 * - Handles error logging and internationalization
 *
 * @example
 * ```typescript
 * // Create unified ability (recommended)
 * const ability = caslFactory.createForUser(profile);
 * const canManageProducts = ability.can('manage', 'Product');
 *
 * // Legacy usage (deprecated but supported)
 * const legacyAbility = caslFactory.createLegacyForUser(profile);
 * const canReadProfiles = legacyAbility.can('read', 'Profile');
 *
 * // Domain-specific access
 * const authAbility = caslFactory.getDomainAbility(profile, 'authorization');
 * const ecommerceAbility = caslFactory.getDomainAbility(profile, 'ecommerce');
 * ```
 */
@Injectable()
export class CaslAbilityFactoryService {
  /**
   * Creates a new CaslAbilityFactoryService instance.
   *
   * @param logger - Logger service for debugging and monitoring
   * @param i18n - Internationalization service for error messages
   * @param unifiedAbilityFactory - Factory for creating unified abilities
   */
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    private readonly unifiedAbilityFactory: UnifiedAbilityFactory, // New dependency
  ) {
    this.logger.setContext(CaslAbilityFactoryService.name);
  }

  /**
   * Creates a unified ability for a user that includes all domain permissions.
   *
   * This is the recommended method for creating abilities in new implementations.
   * It provides comprehensive permission checking across all domains in the application.
   *
   * The unified ability includes:
   * - Authorization domain permissions (Profile, Role, Permission)
   * - E-commerce domain permissions (Product, Order, Cart, CartItem, Review)
   * - Global restrictions and security rules
   * - Domain-specific business logic
   *
   * @param profile - The user profile containing roles and permissions
   * @returns UnifiedAbility - The unified ability for the user
   *
   * @example
   * ```typescript
   * const ability = caslFactory.createForUser(userProfile);
   *
   * // Check permissions across all domains
   * const canManageProducts = ability.can('manage', 'Product');
   * const canReadProfiles = ability.can('read', 'Profile');
   * const canCreateOrders = ability.can('create', 'Order');
   * const canManageAll = ability.can('manage', 'all');
   *
   * // Field-level permissions
   * const canUpdatePrice = ability.can('update', 'Product', 'price');
   * const canReadEmail = ability.can('read', 'Profile', 'email');
   * ```
   */
  createForUser(profile: Profile): UnifiedAbility {
    try {
      const ability = this.unifiedAbilityFactory.createForUser(profile);
      this.logger.log(`Created unified ability for user ${(profile as any).id} (${profile.profileType})`);
      return ability;
    } catch (error) {
      this.logger.error(`Failed to create unified ability for user ${(profile as any).id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Legacy method for backward compatibility.
   *
   * Creates a basic ability focused on authorization domain only.
   * This method is maintained for backward compatibility with existing code
   * but is deprecated in favor of createForUser().
   *
   * @deprecated Use createForUser() instead for full domain support
   * @param profile - The user profile containing roles and permissions
   * @returns AppAbility - The legacy ability for the user
   *
   * @example
   * ```typescript
   * // Legacy usage (deprecated)
   * const legacyAbility = caslFactory.createLegacyForUser(profile);
   * const canReadProfiles = legacyAbility.can('read', 'Profile');
   *
   * // Recommended usage
   * const ability = caslFactory.createForUser(profile);
   * const canReadProfiles = ability.can('read', 'Profile');
   * ```
   */
  createLegacyForUser(profile: Profile): AppAbility {
    const { can, cannot, build } = new AbilityBuilder<AppAbility>(
      PureAbility as AbilityClass<AppAbility>,
    );

    // Apply legacy authorization rules
    this.applyLegacyAuthorizationRules(profile, { can, cannot });

    return build({
      detectSubjectType: (item) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }

  /**
   * Gets a domain-specific ability for a user.
   *
   * This method provides access to domain-specific abilities, allowing you
   * to work with permissions for a specific domain rather than the entire
   * application. This is useful for domain-specific operations or when
   * you need to isolate permission logic.
   *
   * Available domains:
   * - 'authorization': Profile, Role, Permission management
   * - 'ecommerce': Product, Order, Cart, CartItem, Review management
   *
   * @param profile - The user profile
   * @param domainName - Name of the domain (e.g., 'authorization', 'ecommerce')
   * @returns any - The domain-specific ability
   * @throws Error - If the domain factory is not found
   *
   * @example
   * ```typescript
   * // Get authorization domain ability
   * const authAbility = caslFactory.getDomainAbility(profile, 'authorization');
   * const canManageRoles = authAbility.build().can('manage', 'Role');
   *
   * // Get e-commerce domain ability
   * const ecommerceAbility = caslFactory.getDomainAbility(profile, 'ecommerce');
   * const canCreateProduct = ecommerceAbility.build().can('create', 'Product');
   * ```
   */
  getDomainAbility(profile: Profile, domainName: string): any {
    try {
      const ability = this.unifiedAbilityFactory.getDomainAbility(profile, domainName);
      this.logger.log(`Retrieved ${domainName} domain ability for user ${(profile as any).id}`);
      return ability;
    } catch (error) {
      this.logger.error(`Failed to get ${domainName} domain ability for user ${(profile as any).id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Checks if a user has permission for a specific domain.
   *
   * This method provides a convenient way to check permissions within a
   * specific domain without needing to work with the full unified ability.
   * It's useful for domain-specific permission checks.
   *
   * @param profile - The user profile
   * @param domainName - Name of the domain
   * @param action - Action to check
   * @param subject - Subject to check against
   * @returns boolean - True if the user has the permission, false otherwise
   *
   * @example
   * ```typescript
   * // Check e-commerce permissions
   * const canManageProducts = caslFactory.hasDomainPermission(
   *   profile, 'ecommerce', UserActions.MANAGE, 'Product'
   * );
   *
   * // Check authorization permissions
   * const canReadProfiles = caslFactory.hasDomainPermission(
   *   profile, 'authorization', UserActions.READ, 'Profile'
   * );
   * ```
   */
  hasDomainPermission(profile: Profile, domainName: string, action: UserActions, subject: any): boolean {
    try {
      const hasPermission = this.unifiedAbilityFactory.hasDomainPermission(profile, domainName, action, subject);
      this.logger.log(`Checked ${domainName} domain permission for user ${(profile as any).id}: ${action} ${subject} = ${hasPermission}`);
      return hasPermission;
    } catch (error) {
      this.logger.error(`Failed to check ${domainName} domain permission for user ${(profile as any).id}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Gets all available domains.
   *
   * This method returns a list of all registered domain names, which is
   * useful for debugging, administration, or dynamic domain discovery.
   *
   * @returns string[] - Array of available domain names
   *
   * @example
   * ```typescript
   * const domains = caslFactory.getAvailableDomains();
   * console.log('Available domains:', domains); // ['authorization', 'ecommerce']
   *
   * // Use domains for dynamic permission checking
   * for (const domain of domains) {
   *   const hasPermission = caslFactory.hasDomainPermission(profile, domain, UserActions.READ, 'all');
   *   console.log(`${domain}: ${hasPermission}`);
   * }
   * ```
   */
  getAvailableDomains(): string[] {
    try {
      const domains = this.unifiedAbilityFactory.getAvailableDomains();
      this.logger.log(`Retrieved available domains: ${domains.join(', ')}`);
      return domains;
    } catch (error) {
      this.logger.error(`Failed to get available domains: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Registers a new domain factory.
   *
   * This method allows dynamic registration of new domain factories,
   * enabling the system to be extended with new domains without
   * modifying the core factory code.
   *
   * @param domainName - Name of the domain
   * @param factory - Domain ability factory to register
   *
   * @example
   * ```typescript
   * // Register a new inventory domain
   * const inventoryFactory = new InventoryDomainAbilityFactory(logger, i18n);
   * caslFactory.registerDomainFactory('inventory', inventoryFactory);
   *
   * // Now the inventory domain is available
   * const inventoryAbility = caslFactory.getDomainAbility(profile, 'inventory');
   * const canManageInventory = caslFactory.hasDomainPermission(profile, 'inventory', UserActions.MANAGE, 'Inventory');
   * ```
   */
  registerDomainFactory(domainName: string, factory: any): void {
    try {
      this.unifiedAbilityFactory.registerDomainFactory(domainName, factory);
      this.logger.log(`Registered domain factory: ${domainName}`);
    } catch (error) {
      this.logger.error(`Failed to register domain factory ${domainName}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Applies legacy authorization rules for backward compatibility.
   *
   * This method implements the original authorization logic that was used
   * in the legacy system. It provides basic permission checking for
   * Profile and Role entities only.
   *
   * @param profile - The user profile
   * @param abilityBuilder - The CASL ability builder to configure
   *
   * @example
   * ```typescript
   * // This is called internally by createLegacyForUser()
   * this.applyLegacyAuthorizationRules(profile, { can, cannot });
   * ```
   */
  private applyLegacyAuthorizationRules(profile: Profile, abilityBuilder: any): void {
    if (profile.profileType === ProfileType.STAFF) {
      // Staff can manage all if they have MANAGE permission
      if (this.hasPermission(profile, UserActions.MANAGE)) {
        abilityBuilder.can(UserActions.MANAGE, 'all');
        return;
      }

      // Apply specific permissions
      profile.role.permissions.forEach((permission) => {
        const action = permission.name as UserActions;

        switch (action) {
          case UserActions.CREATE:
          case UserActions.READ:
          case UserActions.UPDATE:
            abilityBuilder.can(action, Profile);
            abilityBuilder.can(action, Role);
            break;
          case UserActions.DELETE:
            // More restrictive delete permissions
            this.applyLegacyDeleteRestrictions(profile, abilityBuilder);
            break;
        }
      });

      // Apply field-level restrictions
      this.applyLegacyFieldRestrictions(profile, abilityBuilder);
    } else {
      // Customers have very limited authorization permissions
      abilityBuilder.can(UserActions.READ, Profile, { id: (profile as any).id });
      abilityBuilder.can(UserActions.UPDATE, Profile, { id: (profile as any).id });

      // Cannot access roles
      abilityBuilder.cannot(UserActions.MANAGE, Role);
    }
  }

  /**
   * Applies legacy delete restrictions for backward compatibility.
   *
   * This method implements the original delete restriction logic
   * that was used in the legacy system.
   *
   * @param profile - The user profile
   * @param abilityBuilder - The CASL ability builder to configure
   */
  private applyLegacyDeleteRestrictions(profile: Profile, abilityBuilder: any): void {
    // Prevent deletion of critical entities
    abilityBuilder.cannot(UserActions.DELETE, Profile).because(
              this.i18n.t('errors.cannot_delete', { args: { entity: 'profiles' } })
    );

    abilityBuilder.cannot(UserActions.DELETE, Role).because(
              this.i18n.t('errors.cannot_delete', { args: { entity: 'roles' } })
    );
  }

  /**
   * Applies legacy field restrictions for backward compatibility.
   *
   * This method implements the original field restriction logic
   * that was used in the legacy system.
   *
   * @param profile - The user profile
   * @param abilityBuilder - The CASL ability builder to configure
   */
  private applyLegacyFieldRestrictions(profile: Profile, abilityBuilder: any): void {
    // Restrict sensitive field updates
    const sensitiveFields = ['password', 'role', 'profileType'];

    sensitiveFields.forEach(field => {
      abilityBuilder.cannot(UserActions.UPDATE, Profile, field).because(
        this.i18n.t('errors.restricted_field', { args: { field } })
      );
    });
  }

  /**
   * Checks if a user has a specific permission (legacy method).
   *
   * This method examines the user's role and its associated permissions
   * to determine if they have the specified permission.
   *
   * @param profile - The user profile to check
   * @param permissionName - The name of the permission to check for
   * @returns boolean - True if the user has the permission, false otherwise
   */
  private hasPermission(profile: Profile, permissionName: string): boolean {
    if (!profile.role?.permissions) return false;
    return profile.role.permissions.some(permission => permission.name === permissionName);
  }
}
