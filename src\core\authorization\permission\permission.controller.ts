import { Body, Controller, Get, HttpCode, HttpStatus, Param, Patch, Post, Query } from '@nestjs/common';
import { PermissionService } from './permission.service';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { LoggerService } from '@common/logger/logger.service';
import { CreatePermissionDto } from '@core/authorization/permission/dto/create-permission.dto';
import { CoreUtils } from '@common/utils/core.utils';
import { Permission } from '@core/authorization/permission/entities/permission.entity';
import { I18nService } from 'nestjs-i18n';
import { PermissionDto } from '@core/authorization/permission/dto/permission.dto';
import { PaginationDto } from '@common/pagination.dto';
import { Like } from 'typeorm';
import { Pagination } from 'nestjs-typeorm-paginate';

@ApiTags('Permission Management Endpoints')
@Controller({
  path: 'permission',
  version: '1',
})
export class PermissionController {
  permissionService: any;
  PermissionService: any;
  constructor(
    private readonly roleService: PermissionService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(PermissionController.name);
  }

  @ApiOperation({ summary: 'Create Permission' })
  @ApiBody({ type: CreatePermissionDto })
  @HttpCode(HttpStatus.CREATED)
  @Post('/create')
  async createPermission(@Body() dto: CreatePermissionDto) {
    return CoreUtils.handleRequest(async () => {
      const permission = this.classMapper.map(dto, CreatePermissionDto, Permission);

      await this.permissionService.create(permission);

      return {
        message: this.i18n.t('message.success.created', { args: { entity: 'Permission' } }),
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Modify Permission' })
  @ApiBody({ type: PermissionDto })
  @HttpCode(HttpStatus.OK)
  @Patch('/modify/:permissionId')
  async modifyPermission(@Param('permissionId') permissionId: number, @Body() dto: PermissionDto) {
    return CoreUtils.handleRequest(async () => {
      const existingPermission = await this.permissionService.findByPk(permissionId);
      await this.classMapper.mutateAsync(dto, existingPermission, PermissionDto, Permission);
      await this.permissionService.modify(permissionId, existingPermission);
      return {
        message: this.i18n.t('message.success.modified', { args: { entity: 'Permission' } }),
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Activate Permission(s)' })
  @ApiBody({ type: Number, isArray: true, description: 'Array of Permission IDs' })
  @HttpCode(HttpStatus.OK)
  @Patch('/activate')
  async activatePermission(@Body() ids: number[]) {
    return CoreUtils.handleRequest(async () => {
      await this.permissionService.activate(ids);

      return {
        message: this.i18n.t('message.success.activated', { args: { entity: 'Permission' } }),
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Deactivate Permission(s)' })
  @ApiBody({ type: Number, isArray: true, description: 'Array of Permission IDs' })
  @HttpCode(HttpStatus.OK)
  @Patch('/deactivate')
  async deactivatePermission(@Body() ids: number[]) {
    return CoreUtils.handleRequest(async () => {
      await this.PermissionService.deactivate(ids);

      return {
        message: this.i18n.t('message.success.deactivated', { args: { entity: 'Permission' } }),
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Get permission details' })
  @HttpCode(HttpStatus.OK)
  @Post('/details/:permissionId')
  async getPermissionDetails(@Param('permissionId') permissionId: number) {
    return CoreUtils.handleRequest(async () => {
      const existingPermission = await this.permissionService.findByPk(permissionId);

      return {
        message: this.i18n.t('message.success.retrieved', { args: { entity: 'Permission' } }),
        data: await this.classMapper.mapAsync(existingPermission, Permission, PermissionDto),
      };
    });
  }

  @ApiOperation({ summary: 'Get permissions in table format' })
  @HttpCode(HttpStatus.OK)
  // @ApiQuery({ type: PaginationDto })
  @Get()
  async table(@Query() paginationDto: PaginationDto) {
    return CoreUtils.handleRequest(async () => {
      let { page = 1, limit = 10, search, filter } = paginationDto;

      limit = limit > 100 ? 100 : limit; // limit the pagination to 100
      const where = {};
      if (search) {
        where['name'] = Like(`%${search}%`);
      }
      if (filter) {
        where['status'] = filter;
      }

      const pagination = await this.permissionService.paginate(
        {
          page,
          limit,
          route: `/api/permission`,
        },
        where,
      );

      const result = await this.classMapper.mapArrayAsync(
        pagination.items,
        Permission,
        PermissionDto,
      );

      return {
        message: this.i18n.t('message.success.retrieved', { args: { entity: 'Permission paginated list' } }),
        data: new Pagination(result, pagination.meta, pagination.links),
      };
    });
  }
}
