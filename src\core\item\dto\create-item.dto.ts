import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber, IsPositive, Min } from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class CreateItemDto {
  @AutoMap()
  @IsNotEmpty()
  @IsNumber()
  @ApiProperty({
    description: 'ID of the product this item belongs to',
    example: 1,
    type: Number,
  })
  productId: number;

  @AutoMap()
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'Name of the item',
    example: 'Organic Tomatoes',
    type: String,
  })
  name: string;

  @AutoMap()
  @IsOptional()
  @IsString()
  @ApiProperty({
    description: 'Description of the item',
    example: 'Fresh organic tomatoes from local farms',
    type: String,
    required: false,
  })
  description?: string;

  @AutoMap()
  @IsNotEmpty()
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  @ApiProperty({
    description: 'Price of the item',
    example: 25.99,
    type: Number,
  })
  price: number;

  @AutoMap()
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @ApiProperty({
    description: 'Available units/quantity of the item',
    example: 100,
    type: Number,
  })
  unit: number;
}
