import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { HttpModule } from '@nestjs/axios';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { LoggerModule } from '@common/logger/logger.module';

import { AuthenticationController } from '@core/authentication/authentication.controller';
import { AuthenticationService } from '@core/authentication/authentication.service';
import { AccessTokenStrategy } from '@core/authentication/strategy/access-token.strategy';
import { RefreshTokenStrategy } from '@core/authentication/strategy/refresh-token.stategy';
import { GoogleStrategy } from '@core/authentication/strategy/google.strategy';

import { Role } from '@core/authorization/role/entities/role.entity';
import { Permission } from '@core/authorization/permission/entities/permission.entity';

import { IntegrationModule } from '@core/integration/integration.module';
import { NotificationModule } from '@core/notification/notification.module';

import { Profile } from '@core/profile/entities/profile.entity';
import { ProfileModule } from '@core/profile/profile.module';
import { StaffModule } from '@core/staff/staff.module';
import { CustomerModule } from '@core/customer/customer.module';
import { RedisModule } from '@database/redis/redis.module';

@Module({
  controllers: [AuthenticationController],
  providers: [AuthenticationService, AccessTokenStrategy, RefreshTokenStrategy, GoogleStrategy],
  imports: [
    HttpModule,
    IntegrationModule,
    LoggerModule,
    NotificationModule,
    PassportModule,
    ProfileModule,
    StaffModule,
    CustomerModule,
    RedisModule,
    TypeOrmModule.forFeature([Profile, Role, Permission]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('keys.secret'),
      }),
      inject: [ConfigService],
    }),
    EventEmitterModule.forRoot(),
  ],
  exports: [AuthenticationService],
})
export class AuthenticationModule {}
