import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { LoggerService } from '@common/logger/logger.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { paginate } from 'nestjs-typeorm-paginate';
import { EntityManager, FindManyOptions, ILike, Repository } from 'typeorm';
import { Staff } from './entities/staff.entity';
import { StaffValidationService } from './staff.validation.service';
import { EntityServiceStrategy } from '@common/entity.service.strategy';
import { PaginationQueryParams } from '@common/types/index.type';
import { EntityStatus } from '@common/base.entity';

@Injectable()
export class StaffService implements EntityServiceStrategy<Staff> {
  constructor(
    @InjectRepository(Staff) private readonly staffRepository: Repository<Staff>,
    private readonly staffValidator: StaffValidationService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(StaffService.name);
  }

  async create(data: Staff): Promise<Staff> {
    return await this.entityManager.save(data);
  }

  async modify(id: number, data: Staff): Promise<Staff> {
    await this.staffValidator.validate(data, DatabaseAction.UPDATE);
    return await this.entityManager.save(data);
  }

  async findByPk(id: number): Promise<Staff> {
    const staff = await this.staffRepository.findOneBy({ id });
    if (!staff) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: `Staff with id ${id}` } }));
    }
    return staff;
  }

  async findAllStaff(params: PaginationQueryParams, route: string) {
    const { page, limit, search, filter } = params;
    const where = {};
    if (search) {
      // Search across profile fields (firstName, lastName, email)
      where['profile'] = [{ firstName: ILike(`%${search}%`) }, { lastName: ILike(`%${search}%`) }, { email: ILike(`%${search}%`) }];
    }

    if (filter) {
      where['status'] = filter;
    }

    const options = {
      page,
      limit,
      route,
    };

    const queryOptions: FindManyOptions<Staff> = { where, order: { createdAt: 'desc' } };

    const { items, meta, links } = await paginate(this.staffRepository, options, queryOptions);

    return { items, meta, links };
  }

  async findStaffByProfileId(id: number): Promise<Staff> {
    const staff = await this.staffRepository.findOneBy({ profile: { id } });
    if (!staff) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: `Staff with profile id ${id}` } }));
    }
    return staff;
  }

  async activate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const staff = await this.findByPk(id);
        staff.status = EntityStatus.ACTIVE;
        await this.staffRepository.save(staff);
      }),
    );
  }

  async deactivate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const staff = await this.findByPk(id);
        staff.status = EntityStatus.INACTIVE;
        await this.staffRepository.save(staff);
      }),
    );
  }
}
