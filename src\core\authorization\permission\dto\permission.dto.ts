import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { RoleDto } from '../../role/dto/role.dto';
import { EntityDto } from '@common/base.dto';

export class PermissionDto extends EntityDto {
  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'The name of the permission',
    example: 'Admin',
    type: String,
  })
  name: string;

  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'The description of the permission',
    example: 'Administrator permission with full roles',
    type: String,
  })
  description?: string;

  @AutoMap(() => RoleDto)
  @ApiProperty({
    description: 'List of roles associated with the permission',
    type: RoleDto,
    isArray: true,
    example: [{ id: 1, name: 'CREATE_USER', description: 'Role to create a user' }],
    required: false,
  })
  roles?: Array<RoleDto>;
}
