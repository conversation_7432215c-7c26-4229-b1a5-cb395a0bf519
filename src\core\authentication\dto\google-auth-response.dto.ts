import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean, IsOptional } from 'class-validator';

export class GoogleAuthResponseDto {
  @ApiProperty({
    description: 'JWT access token',
    type: String,
    name: 'accessToken',
  })
  @IsString()
  accessToken: string;

  @ApiProperty({
    description: 'JWT refresh token',
    type: String,
    name: 'refreshToken',
  })
  @IsString()
  refreshToken: string;

  @ApiProperty({
    description: 'User profile information',
    type: Object,
    name: 'profileInfo',
  })
  profileInfo: any;

  @ApiProperty({
    description: 'Whether this is a new user registration',
    required: false,
    type: Boolean,
    name: 'isNewUser',
  })
  @IsBoolean()
  isNewUser: boolean;
}
