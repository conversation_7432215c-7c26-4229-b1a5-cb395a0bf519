import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { CacheModule } from '@nestjs/cache-manager';
import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { MulterModule } from '@nestjs/platform-express';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AcceptLanguageResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import configuration from './config';

import { CommonModule } from '@common/common.module';
import { LoggerModule } from '@common/logger/logger.module';
import { AuthorizationModule } from '@core/authorization/authorization.module';
import { CoreModule } from '@core/core.module';
import { MailModule } from '@core/notification/mail/mail.module';
import { EventsModule } from '@events/events.module';

import { ProfileContextInterceptor } from './profile-context/profile-context.interceptor';
import { CountryModule } from '@core/country/country.module';
import { RedisModule } from '@database/redis/redis.module';
import { rateLimitConfig } from '@config/rate-limit.config';
import { LocationMiddleware } from '@core/location/location.middleware';
import { CountryMiddleware } from '@core/country/country.middleware';

@Module({
  imports: [
    LoggerModule,
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        ...configService.get<object>('database'),
        autoLoadEntities: true,
      }),
      inject: [ConfigService],
    }),
    ScheduleModule.forRoot(),
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    I18nModule.forRoot({
      fallbackLanguage: 'en', // https://nestjs-i18n.com/quick-start
      loaderOptions: {
        path: './src/i18n/',
        watch: true,
      },
      resolvers: [
        { use: QueryResolver, options: ['lang'] },
        AcceptLanguageResolver,
      ],
    }),
    // MailerModule configuration removed to avoid CSS-inline dependency issues
    MulterModule.register({
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
    CacheModule.register({ isGlobal: true }),
    MailModule,
    AuthorizationModule,
    CommonModule,
    CoreModule,
    EventsModule,
    CountryModule,
    RedisModule,
    ThrottlerModule.forRoot([{
      ttl: rateLimitConfig.global.ttl,
      limit: rateLimitConfig.global.limit,
    }]),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ProfileContextInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply TenantMiddleware to all routes to provide country context when available
    // This middleware is flexible and won't block requests without country codes
    consumer.apply(CountryMiddleware).forRoutes(
      {path: 'product/*', method: RequestMethod.ALL, version: '1'},
      {path: 'item/*', method: RequestMethod.ALL, version: '1'},
    );

    // Apply LocationMiddleware to specific routes that need location validation
    // For now, applying to all routes but it's flexible (won't block requests)
    consumer.apply(LocationMiddleware).forRoutes(
      {path: 'product/*', method: RequestMethod.ALL, version: '1'},
      {path: 'item/*', method: RequestMethod.ALL, version: '1'},
    );
  }
}
