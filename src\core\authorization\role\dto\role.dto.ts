import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { PermissionDto } from '../../permission/dto/permission.dto';
import { EntityDto } from '@common/base.dto';

export class RoleDto extends EntityDto {
  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'The name of the role',
    example: 'Admin',
    type: String,
  })
  name: string;

  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'The description of the role',
    example: 'Administrator role with full permissions',
    type: String,
  })
  description?: string;

  @AutoMap(() => PermissionDto)
  @ApiProperty({
    description: 'List of permissions associated with the role',
    type: PermissionDto,
    isArray: true,
    example: [{ id: 1, name: 'CREATE_USER', description: 'Permission to create a user' }],
    required: false,
  })
  permissions?: Array<PermissionDto>;
}
