import { Module } from '@nestjs/common';
import { CountryService } from './country.service';
import { CountryController } from './country.controller';
import { CountryMapperService } from './country.mapper.service';
import { LoggerModule } from '@common/logger/logger.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Country } from '@core/country/entities/country.entity';
import { CountryValidationService } from './country.validation.service';
import { RedisModule } from '@database/redis/redis.module';

@Module({
  controllers: [CountryController],
  exports: [CountryService],
  imports: [LoggerModule, TypeOrmModule.forFeature([Country]), RedisModule], // Add Tenant entity here when created
  providers: [CountryService, CountryMapperService, CountryValidationService],
})
export class CountryModule {}
