import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { LoggerService } from '@common/logger/logger.service';
import { ValidationStrategy } from '@common/validation.strategy';
import { ProductService } from '@core/product/product.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { Repository } from 'typeorm';
import { Item } from './entities/item.entity';

@Injectable()
export class ItemValidationService implements ValidationStrategy<Item> {
  constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Item) private readonly itemRepository: Repository<Item>,
    private readonly productService: ProductService,
    private readonly i18n: I18nService,
  ) {
    this.logger.setContext(ItemValidationService.name);
  }

  async validate(data: Item, action: DatabaseAction) {
    // const existingItem = await this.itemRepository.findOneBy({ id: data.id });

    if (action === DatabaseAction.CREATE) {
      const product = await this.productService.findByPk(data.product.id);
      if (!product) {
        throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Product' } }));
      }
    }

    // if (action === DatabaseAction.UPDATE && !existingItem) {
    //   throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Item' } }));
    // }
  }
}
