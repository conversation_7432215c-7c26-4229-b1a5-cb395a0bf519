import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { LoggerService } from '@common/logger/logger.service';
import { ValidationStrategy } from '@common/validation.strategy';
import { ProductService } from '@core/product/product.service';
import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { Repository, ILike, Not } from 'typeorm';
import { Item } from './entities/item.entity';

@Injectable()
export class ItemValidationService implements ValidationStrategy<Item> {
  constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Item) private readonly itemRepository: Repository<Item>,
    private readonly productService: ProductService,
    private readonly i18n: I18nService,
  ) {
    this.logger.setContext(ItemValidationService.name);
  }

  async validate(data: Item, action: DatabaseAction) {
    if (action === DatabaseAction.CREATE) {
      // Check if product exists
      const product = await this.productService.findByPk(data.product.id);
      if (!product) {
        throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Product' } }));
      }

      // Check if item with the same name already exists
      const existingItemName = await this.itemRepository.findOneBy({
        name: ILike(data.name),
      });
      if (existingItemName) {
        throw new ConflictException(this.i18n.t('message.errors.already_exists', { args: { entity: `Item with name ${data.name}` } }));
      }
    }

    if (action === DatabaseAction.UPDATE) {
      const existingItem = await this.itemRepository.findOneBy({ id: data.id });
      if (!existingItem) {
        throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Item' } }));
      }

      // Check if the new name conflicts with other items (excluding current item)
      const conflictingItem = await this.itemRepository.findOneBy({
        name: ILike(data.name),
        id: Not(data.id),
      });

      if (conflictingItem) {
        throw new ConflictException(this.i18n.t('message.errors.already_exists', { args: { entity: `Item with name ${data.name}` } }));
      }
    }
  }
}
