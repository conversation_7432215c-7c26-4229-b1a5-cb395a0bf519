import { LoggerModule } from '@common/logger/logger.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Product } from './entities/product.entity';
import { ProductController } from './product.controller';
import { ProductMapperService } from './product.mapper.service';
import { ProductService } from './product.service';
import { ProductValidationService } from './product.validation.service';

@Module({
  controllers: [ProductController],
  providers: [ProductService, ProductMapperService, ProductValidationService],
  exports: [ProductService],
  imports: [TypeOrmModule.forFeature([Product]), LoggerModule],
})
export class ProductModule {}
