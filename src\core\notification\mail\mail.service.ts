import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '@common/logger/logger.service';
import * as nodemailer from 'nodemailer';
import { SendEmailDto } from '@core/notification/mail/dto/mail.dto';
import { Resend } from 'resend';

@Injectable()
export class MailService {
  private transporter: nodemailer.Transporter;
  private readonly from: string;
  private readonly resend: Resend;

  constructor(
    private readonly logger: LoggerService,
    private readonly configService: ConfigService,
  ) {
    this.logger.setContext(MailService.name);
    this.from = this.configService.get<string>('mails.defaults.from');

    // Create transporter
    this.transporter = nodemailer.createTransport({
      service: this.configService.get<string>('mails.transport.service'),
      host: this.configService.get<string>('mails.transport.host'),
      port: this.configService.get<number>('mails.transport.port'),
      secure: this.configService.get<boolean>('mails.transport.secure') || false,
      auth: {
        user: this.configService.get<string>('mails.transport.auth.user'),
        pass: this.configService.get<string>('mails.transport.auth.pass'),
      },
    });

    // Initialize Resend client
    this.resend = new Resend(this.configService.get<string>('keys.resendApiKey'));
  }

  /**
   * Send SMTP mail using the default mailer configuration in the application.
   * @param recipient - The recipient of the mail.
   * @param subject - The subject of the mail.
   * @param message - The message to be sent.
   */
  async sendSmtpMail(recipient: string, subject: string, message: any) {
    try {
      const mailOptions = {
        from: `"Green Pastures Team" <${this.from}>`,
        to: recipient,
        subject: subject,
        text: message,
      };

      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Mail sent to ${recipient}, Message ID: ${info.messageId}`);
      return info;
    } catch (err) {
      this.logger.error(`Error sending mail: \n${err?.message}`);
      throw err;
    }
  }

  /**
   * Send SMTP mail using a template.
   * The template is rendered using the context provided.
   * @param recipient - The recipient of the mail.
   * @param subject - The subject of the mail.
   * @param templateUrl - The path to the template file.
   * @param context - The context to render the template.
   */
  async sendSmtpWithTemplate(recipient: string, subject: string, templateUrl: string, context: object) {
    try {
      // For now, we'll send a simple HTML message since template rendering needs to be implemented differently
      const mailOptions = {
        from: `"Green Pastures Team" <${this.from}>`,
        to: recipient,
        subject: subject,
        html: `<p>Template: ${templateUrl}</p><p>Context: ${JSON.stringify(context)}</p>`,
      };

      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Template mail sent to ${recipient}, Message ID: ${info.messageId}`);
      return info;
    } catch (error) {
      this.logger.debug(`Resolved template path: ${templateUrl}`);
      throw new Error(error?.message);
    }
  }

  /**
   * Verify the mailer connection
   */
  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      this.logger.log('Mailer connection verified successfully');
      return true;
    } catch (error) {
      this.logger.error('Mailer connection verification failed:', error.message);
      return false;
    }
  }


  /**
   * Send email using Resend service.
   * @param recipient - The recipient email address.
   * @param subject - The subject of the email.
   * @param message - The plain text message of the email.
   * @param cc - Optional CC email addresses, comma-separated.
   */
  async sendEmail(recipient: string, subject: string, message: string, cc?: string) {
    const from = `"Green Pastures Team" <${this.from}>`
    await this.resend.emails.send({
      from: from,
      to: recipient,
      cc: cc ? cc.split(',').map(email => email.trim()) : undefined,
      subject: subject,
      replyTo: `${this.from}`,
      text: message,
    })
  }
}
