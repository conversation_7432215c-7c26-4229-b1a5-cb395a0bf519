import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateItemTable1754777136730 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
          new Table({
              name: 'item',
              columns: [
                  { name: 'id', type: 'bigint', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
                  { name: 'status', type: 'varchar' },
                  { name: 'created_at', type: 'timestamp', default: 'now()' },
                  { name: 'created_by', type: 'varchar' },
                  { name: 'updated_at', type: 'timestamp', default: 'now()' },
                  { name: 'updated_by', type: 'varchar', isNullable: true },
                  { name: 'deleted_at', type: 'timestamp', isNullable: true },
                  { name: 'name', type: 'varchar' },
                  { name: 'description', type: 'varchar', isNullable: true },
                  { name: 'price', type: 'decimal', default: 0},
                  { name: 'unit', type: 'bigint', default: 0},
                  { name: 'product_id', type: 'bigint', isNullable: true },
              ],
          }),
          true,
        );

        await queryRunner.createIndex(
          'item',
          new TableIndex({
              name: 'IDX_ITEM_FIELDS',
              columnNames: [
                  'id',
                  'status',
                  'created_at',
                  'updated_at',
              ],
          }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropIndex('item', 'IDX_ITEM_FIELDS');
        await queryRunner.dropTable('item');
    }

}
