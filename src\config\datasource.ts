import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { dbConfig } from './database';

config();

const dataSource = new DataSource({
  ...dbConfig(),
  // PostgreSQL specific connection settings
  extra: {
    connectionTimeoutMillis: 60000,
    query_timeout: 60000,
    statement_timeout: 60000,
    idle_timeout: 60000,
    max: 10, // max connections in pool
    min: 1,  // min connections in pool
    // Connection retry settings
    retry: {
      retries: 3,
      factor: 2,
      minTimeout: 1000,
      maxTimeout: 5000,
    },
  },
  // TypeORM specific settings
  migrationsRun: false,
  synchronize: false,
  logging: ['error', 'warn', 'migration'],
});

export default dataSource;
