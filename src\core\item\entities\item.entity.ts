import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Product } from '@core/product/entities/product.entity';
import { AbstractEntity } from '@common/base.entity';

@Entity({ name: 'item' })
export class Item extends AbstractEntity {

  @ManyToOne(() => Product, (product) => product.items)
  @JoinColumn({ name: 'product_id', referencedColumnName: 'id' })
  product: Product;

  @AutoMap()
  @Column({ name: 'name', type: 'varchar', unique: true })
  name: string;

  @AutoMap()
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @AutoMap()
  @Column({ name: 'price', type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @AutoMap()
  @Column({ name: 'unit', type: 'int' })
  unit: number; // If unit is 0 it means the item is not available for sale, if unit is greater than 0 it means the item is available for sale.

}
