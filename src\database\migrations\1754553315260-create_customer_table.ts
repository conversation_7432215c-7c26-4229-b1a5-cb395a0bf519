import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateCustomerTable1754553315260 implements MigrationInterface {

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(new Table({
      name: 'customer',
      columns: [
        { name: 'id', type: 'bigint', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
        { name: 'status', type: 'varchar' },
        { name: 'created_at', type: 'timestamp', default: 'now()' },
        { name: 'created_by', type: 'varchar' },
        { name: 'updated_at', type: 'timestamp', default: 'now()' },
        { name: 'updated_by', type: 'varchar', isNullable: true },
        { name: 'deleted_at', type: 'timestamp', isNullable: true },
        { name: 'referral', type: 'jsonb', isNullable: true },
        { name: 'profile_id', type: 'bigint', isNullable: true },
        { name: 'wallet_details', type: 'jsonb', isNullable: true },
      ],
    }), true);

    await queryRunner.createIndex(
      'customer',
      new TableIndex({
        name: 'IDX_CUSTOMER_FIELDS',
        columnNames: [
          'id',
          'status',
          'created_at',
          'updated_at',
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('customer', 'IDX_CUSTOMER_FIELDS');
    await queryRunner.dropTable('customer');
  }

}
