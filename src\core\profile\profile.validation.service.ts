import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { LoggerService } from '@common/logger/logger.service';
import { BadRequestException, ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { Repository } from 'typeorm';
import { Profile } from './entities/profile.entity';
import { ValidationStrategy } from '@common/validation.strategy';

@Injectable()
export class ProfileValidationService implements ValidationStrategy<Profile> {
  constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Profile)
    private readonly profileRepo: Repository<Profile>,
    private readonly i18n: I18nService,
  ) {
    this.logger.setContext(ProfileValidationService.name);
  }
  async validate(data: Profile, action: DatabaseAction) {
    try {
      const existingProfile = await this.profileRepo.findOne({
        where: [{ email: data.email }, { phoneNumber: data.phoneNumber }],
        select: ['id', 'email'], // Only select needed fields
      });

      if (action === DatabaseAction.CREATE) {
        if (existingProfile) {
          const isEmailConflict = existingProfile.email === data.email;
          throw new ConflictException(
            this.i18n.t('errors.already_exists', {
              args: isEmailConflict ? { entity: 'Email' } : { entity: 'Phone number' },
            }),
          );
        }
      } else if (action === DatabaseAction.UPDATE) {
        if (!existingProfile) {
          throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Profile' } }));
        }
        if (existingProfile.id !== data.id) {
          throw new BadRequestException(this.i18n.t('errors.email_taken'));
        }
      }
    } catch (error) {
              this.logger.error(this.i18n.t('errors.validation_error', { args: { entity: 'Profile' } }), `${error.message}`, error.stack);
      throw error;
    }
  }
}
