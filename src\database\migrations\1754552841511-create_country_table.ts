import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateCountryTable1754552841511 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
          new Table({
              name: 'country',
              columns: [
                  { name: 'id', type: 'bigint', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
                  { name: 'status', type: 'varchar' },
                  { name: 'created_at', type: 'timestamp', default: 'now()' },
                  { name: 'created_by', type: 'varchar' },
                  { name: 'updated_at', type: 'timestamp', default: 'now()' },
                  { name: 'updated_by', type: 'varchar', isNullable: true },
                  { name: 'deleted_at', type: 'timestamp', isNullable: true },
                  {name: 'name', type: 'varchar',  isUnique: true, length: '50'},
                  {name: 'code', type: 'varchar',  isUnique: true, length: '20'},
                  {name: 'price_factor', type: 'decimal', precision: 10, scale: 2, default: 1.00},
                  {name: 'currency_code', type: 'varchar', length: '3'},
                {name: 'is_blocked', type: 'boolean', default: false}
              ]
          }),
          true
        )

        await queryRunner.createIndex(
          'country',
          new TableIndex({
              name: 'IDX_COUNTRY_FIELDS',
              columnNames: [
                  'id',
                  'status',
                  'created_at',
                  'updated_at',
                  'name',
              ],
          }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropIndex('country', 'IDX_COUNTRY_FIELDS');
        await queryRunner.dropTable('country');
    }

}
