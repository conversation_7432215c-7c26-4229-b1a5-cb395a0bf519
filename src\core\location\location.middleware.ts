import { ForbiddenException, Injectable, NestMiddleware } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { CountryService } from '@core/country/country.service';
import { RedisService } from '@database/redis/redis.service';
import { NextFunction, Request, Response } from 'express';
import { Country } from '@core/country/entities/country.entity';

/**
 * LocationMiddleware - Provides location-based functionality without strict enforcement
 *
 * This middleware:
 * - Provides country context when country codes are available
 * - Skips validation gracefully when country codes are missing
 * - Only blocks requests from explicitly blocked countries
 * - Attaches country information to request for optional use
 *
 * It's designed to be flexible - applications can use country context when available
 * but aren't required to provide country codes for every request.
 */
@Injectable()
export class LocationMiddleware implements NestMiddleware {
  constructor(
    private readonly logger: LoggerService,
    private readonly countryService: CountryService,
    private readonly redisService: RedisService
  ){
    this.logger.setContext(LocationMiddleware.name)
  }

  async use(req: Request, res: Response, next: NextFunction) {
    const countryCode = req.headers['x-country-code'] as string;
    let country: Country;

    if (!countryCode) {
      this.logger.debug('Country code header is missing, skipping location validation');
      return next();
    }

    // Check Redis cache first
    if (await this.redisService.isKeyExists(`country:${countryCode}`)) {
      country = await this.redisService.getCacheKey(`country:${countryCode}`);
    }

    // If not in cache, fetch from database
    if (!country) {
      country = await this.countryService.findByCountryCode(countryCode);
    }

    if (!country) {
      this.logger.warn(`Country not supported: ${countryCode}`);
      // Don't throw error, just log and continue
      return next();
    }

    if (country.isBlocked) {
      this.logger.warn(`Access denied for blocked country: ${countryCode}`);
      throw new ForbiddenException('Access denied for your country');
    }

    // Attach country to request for later use (e.g. in interceptor)
    req['country'] = country;
    this.logger.debug(`Location validation successful for country: ${countryCode}`);

    next();
  }
}
