import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SeedRoleDto } from '@core/authorization/role/dto/seed-role.dto';

export class SeedPermissionDto {
  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    name: 'name',
    description: 'The name of the permission',
    required: true,
  })
  name: string;

  @AutoMap(() => String)
  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    name: 'description',
    description: 'The description of the permission',
  })
  description?: string;

  @AutoMap(() => SeedRoleDto)
  @ApiProperty({ type: SeedRoleDto, isArray: true })
  permissions?: SeedRoleDto[];
}
