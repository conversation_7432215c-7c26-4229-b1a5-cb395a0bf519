import { Body, Controller, Get, HttpCode, HttpStatus, Param, Patch, Post, Query } from '@nestjs/common';
import { RoleService } from './role.service';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { LoggerService } from '@common/logger/logger.service';
import { CreateRoleDto } from '@core/authorization/role/dto/create-role.dto';
import { CoreUtils } from '@common/utils/core.utils';
import { Role } from '@core/authorization/role/entities/role.entity';
import { I18nService } from 'nestjs-i18n';
import { RoleDto } from '@core/authorization/role/dto/role.dto';
import { PaginationDto } from '@common/pagination.dto';
import { Like } from 'typeorm';
import { Pagination } from 'nestjs-typeorm-paginate';

@ApiTags('Role Management Endpoints')
@Controller({
  path: 'role',
  version: '1',
})
export class RoleController {
  constructor(
    private readonly roleService: RoleService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(RoleController.name);
  }

  @ApiOperation({ summary: 'Create Role' })
  @ApiBody({ type: CreateRoleDto })
  @HttpCode(HttpStatus.CREATED)
  @Post('/create')
  async createRole(@Body() dto: CreateRoleDto) {
    return CoreUtils.handleRequest(async () => {
      const role = this.classMapper.map(dto, CreateRoleDto, Role);

      await this.roleService.create(role);

      return {
        message: this.i18n.t('message.success.created', { args: { entity: 'Role' } }),
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Modify Role' })
  @ApiBody({ type: RoleDto })
  @HttpCode(HttpStatus.OK)
  @Patch('/modify/:roleId')
  async modifyRole(@Param('roleId') roleId: number, @Body() dto: RoleDto) {
    return CoreUtils.handleRequest(async () => {
      const existingRole = await this.roleService.findByPk(roleId);
      await this.classMapper.mutateAsync(dto, existingRole, RoleDto, Role);
      await this.roleService.modify(roleId, existingRole);
      return {
        message: this.i18n.t('message.success.modified', { args: { entity: 'Role' } }),
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Activate Role(s)' })
  @ApiBody({ type: Number, isArray: true, description: 'Array of Role IDs' })
  @HttpCode(HttpStatus.OK)
  @Patch('/activate')
  async activateRole(@Body() ids: number[]) {
    return CoreUtils.handleRequest(async () => {
      await this.roleService.activate(ids);

      return {
        message: this.i18n.t('message.success.activated', { args: { entity: 'Role' } }),
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Deactivate Role(s)' })
  @ApiBody({ type: Number, isArray: true, description: 'Array of Role IDs' })
  @HttpCode(HttpStatus.OK)
  @Patch('/deactivate')
  async deactivateRole(@Body() ids: number[]) {
    return CoreUtils.handleRequest(async () => {
      await this.roleService.deactivate(ids);

      return {
        message: this.i18n.t('message.success.deactivated', { args: { entity: 'Role' } }),
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Get role details' })
  @HttpCode(HttpStatus.OK)
  @Post('/details/:roleId')
  async getRoleDetails(@Param('roleId') roleId: number) {
    return CoreUtils.handleRequest(async () => {
      const existingRole = await this.roleService.findByPk(roleId);

      return {
        message: this.i18n.t('message.success.retrieved', { args: { entity: 'Role' } }),
        data: await this.classMapper.mapAsync(existingRole, Role, RoleDto),
      };
    });
  }

  @ApiOperation({ summary: 'Get roles in table format' })
  @HttpCode(HttpStatus.OK)
  // @ApiQuery({ type: PaginationDto })
  @Get()
  async table(@Query() paginationDto: PaginationDto) {
    return CoreUtils.handleRequest(async () => {
      let { page = 1, limit = 10, search, filter } = paginationDto;

      limit = limit > 100 ? 100 : limit; // limit the pagination to 100
      const where = {};
      if (search) {
        where['name'] = Like(`%${search}%`);
      }
      if (filter) {
        where['status'] = filter;
      }

      const pagination = await this.roleService.paginate(
        {
          page,
          limit,
          route: `/api/role`,
        },
        where,
      );

      const result = await this.classMapper.mapArrayAsync(
        pagination.items,
        Role,
        RoleDto,
      );

      return {
        message: this.i18n.t('message.success.retrieved', { args: { entity: 'Role paginated list' } }),
        data: new Pagination(result, pagination.meta, pagination.links),
      };
    });
  }
}
