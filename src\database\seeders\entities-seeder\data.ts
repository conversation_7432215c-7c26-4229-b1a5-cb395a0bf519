import { SeedRoleDto } from '@core/authorization/role/dto/seed-role.dto';
import { CoreConstants } from '@common/utils/core.constants';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { CoreUtils } from '@common/utils/core.utils';
import { UserActions } from '@common/enumerations/user_actions.enum';
import { SeedProfileDto } from '@core/profile/dto/seed-profile.dto';

export const roles: Array<SeedRoleDto> = [
  { name: CoreConstants.SUPER_ADMIN_ROLE, description: 'Super Admin role.' },
];

export const accounts: Array<SeedProfileDto> = [
  {
    firstName: 'Nicolette',
    lastName: 'Sanders',
    accountType: ProfileType.STAFF,
    email: '<EMAIL>',
    password: CoreUtils.hashPassword('Gr33nP@stur3'),
    phoneNumber: '+*************',
    role: {
      name: CoreConstants.SUPER_ADMIN_ROLE,
      description: 'Super Admin role.',
      permissions: [
        { name: UserActions.MANAGE, description: 'Can Manage Record' },
      ],
    },
  },
];

export const permissions: Array<{ name: string, description: string }> = [
  { name: UserActions.CREATE, description: 'Can Create Record' },
  { name: UserActions.DELETE, description: 'Can Delete Record' },
  { name: UserActions.READ, description: 'Can Read Record' },
  { name: UserActions.MANAGE, description: 'Can Manage Record' },
  { name: UserActions.UPDATE, description: 'Can Modify Record' },
];

// Seed data for countries (tenants)
export const countries: Array<{ name: string; code: string, priceFactor: number, currencyCode: string, isBlocked: boolean }> = [
  { name: 'Nigeria', code: 'NG', priceFactor: 1.00, currencyCode: 'NGN', isBlocked: false },
  { name: 'United States', code: 'US', priceFactor: 1.54, currencyCode: 'USD', isBlocked: false },
  { name: 'United Kingdom', code: 'GB', priceFactor: 1.56, currencyCode: 'GBP', isBlocked: false },
  { name: 'Canada', code: 'CA', priceFactor: 1.23, currencyCode: 'CAD', isBlocked: false },
];
