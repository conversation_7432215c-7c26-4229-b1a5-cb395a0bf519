import { Injectable, NotFoundException } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { Permission } from './entities/permission.entity';
import { I18nService } from 'nestjs-i18n';
import { InjectRepository } from '@nestjs/typeorm';
import { PermissionValidationService } from './permission.validation.service';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { EntityServiceStrategy } from '@common/entity.service.strategy';
import { EntityStatus } from '@common/base.entity';
import { Repository } from 'typeorm';

@Injectable()
export class PermissionService implements EntityServiceStrategy<Permission> {
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectRepository(Permission)
    private readonly permissionRepo: Repository<Permission>,
    private readonly permissionValidator: PermissionValidationService,
  ) {
    this.logger.setContext(PermissionService.name);
  }

  async activate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const permission: Permission = await this.permissionRepo.findOne({
          where: { id: id },
        });
        permission.status = EntityStatus.ACTIVE;
        await this.permissionRepo.save(permission);
      }),
    );
  }

  async create(data: Permission): Promise<Permission> {
    await this.permissionRepo.save(data);
    return Promise.resolve(await this.permissionRepo.save(data));
  }

  async deactivate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const permission: Permission = await this.permissionRepo.findOne({
          where: { id: id },
        });
        permission.status = EntityStatus.INACTIVE;
        await this.permissionRepo.save(permission);
      }),
    );
  }

  async findByPk(id: number): Promise<Permission | null> {
    return Promise.resolve(
      await this.permissionRepo.findOne({ where: { id: id } }),
    );
  }

  async modify(id: number, data: Permission): Promise<Permission> {
    await this.permissionValidator.validate(data, DatabaseAction.UPDATE);

    if (!(await this.findByPk(id)))
      throw new NotFoundException(
        this.i18n.t('errors.permission_not_found'),
      );
    return Promise.resolve(await this.permissionRepo.save(data));
  }
}
