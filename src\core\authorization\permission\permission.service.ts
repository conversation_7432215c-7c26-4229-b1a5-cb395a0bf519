import { Injectable, NotFoundException } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/entity.service.strategy';
import { Permission } from './entities/permission.entity';
import { EntityStatus } from '@common/base.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PermissionValidationService } from './permission.validation.service';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { I18nService } from 'nestjs-i18n';
import { IPaginationOptions, Pagination, paginate } from 'nestjs-typeorm-paginate';

@Injectable()
export class PermissionService implements EntityServiceStrategy<Permission> {
  constructor(
    private readonly loggerService: LoggerService,
    @InjectRepository(Permission) private readonly permissionRepo: Repository<Permission>,
    private readonly permissionValidator: PermissionValidationService,
    private readonly i18n: I18nService,
  ) {
    this.loggerService.setContext(PermissionService.name);
  }

  async activate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const permission: Permission = await this.permissionRepo.findOne({
          where: { id: id },
        });
        permission.status = EntityStatus.ACTIVE;
        await this.permissionRepo.save(permission);
      }),
    );
  }

  async create(data: Permission): Promise<Permission> {
    await this.permissionValidator.validate(data, DatabaseAction.CREATE);
    return Promise.resolve(await this.permissionRepo.save(data));
  }

  async deactivate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const permission: Permission = await this.permissionRepo.findOne({
          where: { id: id },
        });
        permission.status = EntityStatus.INACTIVE;
        await this.permissionRepo.save(permission);
      }),
    );
  }

  async findByPk(id: number): Promise<Permission | null> {
    return Promise.resolve(await this.permissionRepo.findOne({ where: { id: id } }));
  }

  async modify(id: number, data: Permission): Promise<Permission> {
    await this.permissionValidator.validate(data, DatabaseAction.UPDATE);
    const permission = await this.findByPk(id);

    if (!permission)
      throw new NotFoundException(this.i18n.t('message.errors.not_found', {args: { entity: "Permission" }}));

    return Promise.resolve(await this.permissionRepo.save(data));
  }

  /**
   * Paginate through permissions with optional filtering criteria.
   * @param options
   * @param where
   */
  async paginate(options: IPaginationOptions, where?: any): Promise<Pagination<Permission>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<Permission>(this.permissionRepo, options, {
        where,
        order: {
          createdAt: 'DESC'
        }
      });
    }
    return paginate<Permission>(this.permissionRepo, options);
  }
}
