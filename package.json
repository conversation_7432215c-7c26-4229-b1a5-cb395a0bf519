{"name": "green-api", "version": "0.0.1", "description": "", "author": "<PERSON>", "private": true, "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node --max-old-space-size=1024 dist/main", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node --max-old-space-size=2048 dist/main", "build:prod": "node --max-old-space-size=2048 ./node_modules/@nestjs/cli/bin/nest.js build", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli", "typeorm:run-migrations": "npm run typeorm migration:run -- -d ./src/config/datasource.ts", "typeorm:generate-migration": "cross-var npm run typeorm -- -d ./src/config/datasource.ts migration:generate ./src/database/migrations/%npm_config_name%", "typeorm:create-migration": "cross-var npm run typeorm -- migration:create ./src/database/migrations/%npm_config_name%", "typeorm:revert-migration": "npm run typeorm -- -d ./src/config/datasource.ts migration:revert", "drop:database": "npm run typeorm -- -d ./src/config/datasource.ts schema:drop", "db:seed": "ts-node -r tsconfig-paths/register ./src/seed.ts"}, "dependencies": {"@automapper/classes": "^8.8.1", "@automapper/core": "^8.8.1", "@automapper/nestjs": "^8.8.1", "@casl/ability": "6", "@keyv/redis": "^5.1.0", "@nestjs/axios": "^4.0.1", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "11.0.16", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.3.3", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.3.3", "@nestjs/platform-socket.io": "^11.1.6", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^7.3.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^10.0.1", "@nestjs/websockets": "^11.1.6", "@nestlab/google-recaptcha": "^3.10.0", "@onesignal/node-onesignal": "5.2.0-beta1", "@types/bcrypt": "^6.0.0", "axios": "^1.11.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "buffer-to-stream": "^1.0.0", "cache-manager": "^7.1.1", "cacheable": "^1.10.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cloudinary": "^2.7.0", "cron": "^4.3.3", "cross-var": "^1.1.0", "dotenv": "^17.2.1", "dynamic-string": "^0.1.2", "handlebars": "^4.7.8", "hbs": "^4.2.0", "jsonwebtoken": "^9.0.2", "keyv": "^5.5.0", "lodash": "^4.17.21", "luxon": "^3.7.1", "mime-type": "^5.0.3", "mime-types": "^3.0.1", "nestjs-i18n": "^10.5.1", "nestjs-paginate": "^12.5.1", "nestjs-typeorm-paginate": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-google-oauth2": "^0.2.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "resend": "^6.0.1", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "stripe": "^18.4.0", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.20"}, "devDependencies": {"@nestjs/cli": "^11.0.10", "@nestjs/schematics": "^11.0.7", "@nestjs/testing": "^11.1.6", "@types/bcryptjs": "^3.0.0", "@types/express": "^5.0.3", "@types/jest": "30.0.0", "@types/mime-types": "^3.0.1", "@types/multer": "^2.0.0", "@types/node": "^24.2.1", "@types/node-fetch": "^2.6.13", "@types/nodemailer": "^6.4.17", "@types/passport-google-oauth2": "^0.1.10", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "cross-env": "^10.0.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "jest": "30.0.5", "prettier": "^3.6.2", "source-map-support": "^0.5.21", "supertest": "^7.1.4", "ts-jest": "29.4.1", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^5.9.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}