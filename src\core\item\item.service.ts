import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository, InjectEntityManager } from '@nestjs/typeorm';
import { Repository, EntityManager, FindManyOptions, ILike } from 'typeorm';
import { I18nService } from 'nestjs-i18n';
import { paginate, IPaginationOptions, Pagination } from 'nestjs-typeorm-paginate';
import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/entity.service.strategy';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { EntityStatus } from '@common/base.entity';
import { PaginationQueryParams } from '@common/types/index.type';
import { Item } from './entities/item.entity';
import { Product } from '@core/product/entities/product.entity';
import { ItemValidationService } from './item.validation.service';
import { CreateItemDto } from './dto/create-item.dto';
import { ProductService } from '../product/product.service';

@Injectable()
export class ItemService implements EntityServiceStrategy<Item> {
  constructor(
    @InjectRepository(Item) private readonly itemRepository: Repository<Item>,
    private readonly itemValidator: ItemValidationService,
    private readonly productService: ProductService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(ItemService.name);
  }

  async create(data: Item): Promise<Item> {
    const product = await this.productService.findByPk(data.product.id);
    if (!product) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: `Product with ${data.product.id}` } }));
    }
    return await this.entityManager.save(data);
  }

  async modify(id: number, data: Item): Promise<Item> {
    await this.itemValidator.validate(data, DatabaseAction.UPDATE);
    return await this.itemRepository.save(data);
  }

  async findByPk(id: number): Promise<Item | null> {
    const item = await this.itemRepository.findOneBy({ id });
    if (!item) {
      throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Item' } }));
    }
    return item;
  }

  async findAllItems(params: PaginationQueryParams, route: string) {
    const { page, limit, search, filter } = params;
    const where = {};

    if (search) {
      where['name'] = ILike(`%${search}%`);
    }

    if (filter) {
      where['status'] = filter;
    }

    const options = {
      page,
      limit,
      route,
    };

    const queryOptions: FindManyOptions<Item> = {
      where,
      order: { createdAt: 'desc' },
      relations: ['product'],
    };

    const { items, meta, links } = await paginate(this.itemRepository, options, queryOptions);

    return { items, meta, links };
  }

  async activate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const item = await this.findByPk(id);
        item.status = EntityStatus.ACTIVE;
        await this.itemRepository.save(item);
      }),
    );
  }

  async deactivate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const item = await this.findByPk(id);
        item.status = EntityStatus.INACTIVE;
        await this.itemRepository.save(item);
      }),
    );
  }

  async remove(id: number): Promise<void> {
    const item = await this.findByPk(id);
    await this.itemRepository.remove(item);
  }

  /**
   * Paginate through items with optional filtering criteria.
   * @param options
   * @param where
   */
  async paginate(options: IPaginationOptions, where?: any): Promise<Pagination<Item>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<Item>(this.itemRepository, options, {
        where,
        order: {
          createdAt: 'DESC',
        },
        relations: ['product'],
      });
    }
    return paginate<Item>(this.itemRepository, options, {
      order: {
        createdAt: 'DESC',
      },
      relations: ['product'],
    });
  }
}
