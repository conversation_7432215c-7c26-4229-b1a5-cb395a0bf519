import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateStoreTable1754641070493 implements MigrationInterface {

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'store',
        columns: [
          { name: 'id', type: 'bigint', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
          { name: 'status', type: 'varchar' },
          { name: 'created_at', type: 'timestamp', default: 'now()' },
          { name: 'created_by', type: 'varchar' },
          { name: 'updated_at', type: 'timestamp', default: 'now()' },
          { name: 'updated_by', type: 'varchar', isNullable: true },
          { name: 'deleted_at', type: 'timestamp', isNullable: true },
          { name: 'name', type: 'varchar', length: '100' },
          { name: 'address', type: 'jsonb', isNullable: true },
        ],
      }),
      true,
    );

    await queryRunner.createIndex(
      'store',
      new TableIndex({
        name: 'IDX_STORE_FIELDS',
        columnNames: [
          'id',
          'status',
          'created_at',
          'updated_at',
          'name',
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('store', 'IDX_STORE_FIELDS');
    await queryRunner.dropTable('store');
  }

}
