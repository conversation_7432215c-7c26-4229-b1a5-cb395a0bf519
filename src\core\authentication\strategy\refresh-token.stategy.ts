import { Injectable } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { ExtractJwt, Strategy } from "passport-jwt";
import { ConfigService } from "@nestjs/config";
import { JwtPayload } from "jsonwebtoken";

interface JwtPayloadWithType extends JwtPayload {
  type?: string;
}

@Injectable()
export class RefreshTokenStrategy extends PassportStrategy(
  Strategy,
  'jwt-refresh',
) {
  constructor(
    private configService: ConfigService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.get<string>('keys.secret'),
      passReqToCallback: true,
    });
  }

  async validate(request: any, payload: JwtPayloadWithType) {
    const { type } = payload;
    
    // Validate token type
    if (type !== 'refresh') {
      throw new Error('Invalid token type');
    }

    const refreshToken = request
      .get('Authorization')
      .replace('Bearer', '')
      .trim();

    return { 
      userId: payload.sub, 
      email: payload.email, 
      refreshToken
    };
  }
}
