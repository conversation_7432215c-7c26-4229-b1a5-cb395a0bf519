{
  "compilerOptions": {
    "module": "commonjs",
    "lib": ["esnext", "dom"],
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2022",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "tsBuildInfoFile": "./dist/tsconfig.tsbuildinfo",
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "paths": {
      "@common/*": ["src/common/*"],
      "@config/*": ["src/config/*"],
      "@core/*": ["src/core/*"],
      "@database/*": ["src/database/*"],
      "@events/*": ["src/events/*"],
//      "@i18n/*": ["src/i18n/*"],
      "@test/*": ["src/test/*"]
    }
  }
}
