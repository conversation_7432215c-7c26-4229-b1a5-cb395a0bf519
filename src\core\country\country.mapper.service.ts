import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { createMap, Mapper, MappingProfile } from '@automapper/core';
import { Country } from '@core/country/entities/country.entity';
import { CountryDto } from '@core/country/dto/country.dto';
import { CreateCountryDto } from '@core/country/dto/create-country.dto';

@Injectable()
export class CountryMapperService extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, Country, CountryDto);
      createMap(mapper, CountryDto, Country);
      createMap(mapper, CreateCountryDto, Country);
    };
  }
}
