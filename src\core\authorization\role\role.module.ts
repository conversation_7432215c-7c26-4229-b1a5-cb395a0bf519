import { <PERSON>du<PERSON> } from '@nestjs/common';
import { RoleService } from './role.service';
import { RoleController } from './role.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Role } from './entities/role.entity';
import { LoggerModule } from '@common/logger/logger.module';
import { RoleMapperService } from './role.mapper.service';
import { RoleValidationService } from './role.validation.service';

@Module({
  controllers: [RoleController],
  providers: [RoleService, RoleMapperService, RoleValidationService],
  imports: [TypeOrmModule.forFeature([Role]), LoggerModule],
  exports: [RoleService],
})
export class RoleModule {}
