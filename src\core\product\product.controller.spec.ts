import { Test, TestingModule } from '@nestjs/testing';
import { ProductController } from './product.controller';
import { ProductService } from './product.service';
import { Mapper } from '@automapper/core';
import { I18nService } from 'nestjs-i18n';
import { LoggerService } from '@common/logger/logger.service';

describe('ProductController', () => {
  let controller: ProductController;
  let service: ProductService;

  const mockProductService = {
    findAllProducts: jest.fn(),
    findOneProduct: jest.fn(),
    create: jest.fn(),
    modify: jest.fn(),
    activate: jest.fn(),
    deactivate: jest.fn(),
    remove: jest.fn(),
  };

  const mockMapper = {
    mapArrayAsync: jest.fn(),
    mapAsync: jest.fn(),
  };

  const mockI18nService = {
    t: jest.fn().mockReturnValue('Success message'),
  };

  const mockLoggerService = {
    setContext: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProductController],
      providers: [
        {
          provide: ProductService,
          useValue: mockProductService,
        },
        {
          provide: 'Mapper',
          useValue: mockMapper,
        },
        {
          provide: I18nService,
          useValue: mockI18nService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    controller = module.get<ProductController>(ProductController);
    service = module.get<ProductService>(ProductService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllProducts', () => {
    it('should return paginated products', async () => {
      const mockProducts = [{ id: 1, name: 'Test Product' }];
      const mockPagination = {
        items: mockProducts,
        meta: { totalItems: 1 },
        links: {},
      };

      mockProductService.findAllProducts.mockResolvedValue(mockPagination);
      mockMapper.mapArrayAsync.mockResolvedValue(mockProducts);

      const result = await controller.getAllProducts(1, 10, '', '', '/products');

      expect(service.findAllProducts).toHaveBeenCalledWith({ page: 1, limit: 10, search: '', filter: '' }, '/products');
      expect(result).toBeDefined();
    });
  });

  describe('getOneProduct', () => {
    it('should return a single product', async () => {
      const mockProduct = { id: 1, name: 'Test Product' };
      mockProductService.findOneProduct.mockResolvedValue(mockProduct);
      mockMapper.mapAsync.mockResolvedValue(mockProduct);

      const result = await controller.getOneProduct(1);

      expect(service.findOneProduct).toHaveBeenCalledWith(1);
      expect(result).toBeDefined();
    });
  });

  describe('createProduct', () => {
    it('should create a new product', async () => {
      const createDto = { name: 'New Product', description: 'Description' };
      const mockProduct = { id: 1, ...createDto };

      mockMapper.mapAsync.mockResolvedValue(mockProduct);
      mockProductService.create.mockResolvedValue(mockProduct);

      const result = await controller.createProduct(createDto);

      expect(service.create).toHaveBeenCalled();
      expect(result).toBeDefined();
    });
  });

  describe('activateProducts', () => {
    it('should activate products', async () => {
      const activateDto = { ids: [1, 2] };
      mockProductService.activate.mockResolvedValue(undefined);

      const result = await controller.activateProducts(activateDto);

      expect(service.activate).toHaveBeenCalledWith([1, 2]);
      expect(result).toBeDefined();
    });
  });

  describe('deactivateProducts', () => {
    it('should deactivate products', async () => {
      const deactivateDto = { ids: [1, 2] };
      mockProductService.deactivate.mockResolvedValue(undefined);

      const result = await controller.deactivateProducts(deactivateDto);

      expect(service.deactivate).toHaveBeenCalledWith([1, 2]);
      expect(result).toBeDefined();
    });
  });

  describe('deleteProduct', () => {
    it('should delete a product', async () => {
      mockProductService.remove.mockResolvedValue(undefined);

      const result = await controller.deleteProduct(1);

      expect(service.remove).toHaveBeenCalledWith(1);
      expect(result).toBeDefined();
    });
  });
});
