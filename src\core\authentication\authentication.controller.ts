import { Body, Controller, Get, Headers, HttpCode, HttpStatus, Post, Req, UseGuards } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Public } from '@common/decorators/public.decorator';
import { CoreUtils } from '@common/utils/core.utils';
import { I18nService } from 'nestjs-i18n';
import { AuthenticationService } from '@core/authentication/authentication.service';
import { SignInDto } from '@core/authentication/dto/sign-in.dto';
import { ForgotPasswordDto } from '@core/authentication/dto/forgot-password.dto';
import { CreateProfileDto } from '@core/profile/dto/create-profile.dto';
import { VerifyAccountDto } from './dto/verify-account.dto';
import { ResendVerificationOtpDto } from './dto/resend-verification-otp.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { GoogleAuthTokenDto } from './dto/google-auth-token.dto';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Profile } from '@core/profile/entities/profile.entity';
import { ProfileDto } from '@core/profile/dto/profile.dto';
import { RefreshTokenGuard } from './guard/refresh-token.guard';
import { JwtAuthGuard } from './guard/jwt-auth.guard';
import { Throttle } from '@nestjs/throttler';
import { rateLimitConfig } from '@config/rate-limit.config';

@ApiTags('Authentication Management Endpoints')
@Controller({
  path: 'auth',
  version: '1',
})
export class AuthenticationController {

  constructor(
    private readonly logger: LoggerService,
    private readonly authService: AuthenticationService,
    private readonly i18n: I18nService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(AuthenticationController.name);
  }

  @Public()
  @Throttle({ default: { ttl: rateLimitConfig.auth.signup.ttl, limit: rateLimitConfig.auth.signup.limit } })
  @ApiOperation({ summary: 'User Registration' })
  @ApiBody({ type: CreateProfileDto })
  @HttpCode(HttpStatus.CREATED)
  @Post('signup')
  async signup(@Body() createProfileDto: CreateProfileDto) {
    return CoreUtils.handleRequest(async () => {

      const profile = this.classMapper.map(createProfileDto, CreateProfileDto, Profile);

      await this.authService.signup(profile);
      return {
        message: this.i18n.t('message.success.registration'),
        data: null,
      };
    });
  }

  @Public()
  @Throttle({ default: { ttl: rateLimitConfig.auth.login.ttl, limit: rateLimitConfig.auth.login.limit } })
  @ApiOperation({ summary: 'User Sign-In' })
  @ApiBody({ type: SignInDto })
  @HttpCode(HttpStatus.OK)
  @Post('login')
  async login(@Body() loginDto: SignInDto) {
    return CoreUtils.handleRequest(async () => {
      const { email, password } = loginDto;

      const result = await this.authService.signin(email, password);
      const data = {
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        profileInfo: this.classMapper.map(result.profile, Profile, ProfileDto),
      };
      return { message: this.i18n.t('message.success.login'), data };
    });
  }

  @Public()
  @UseGuards(RefreshTokenGuard)
  @ApiOperation({ summary: 'Refresh Access Token' })
  @HttpCode(HttpStatus.OK)
  @Post('refresh')
  async refreshTokens(@Req() req: any, @Headers() headers: any) {
    return CoreUtils.handleRequest(async () => {
      const { userId, email, refreshToken} = req.user;

      const result = await this.authService.refreshTokens(
        userId,
        email,
        refreshToken,
      );

      const data = {
        accessToken: result.accessToken,
        refreshToken: result.refreshToken
      };
      return { message: this.i18n.t('message.success.tokens_refreshed'), data };
    });
  }

  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'User Logout' })
  @HttpCode(HttpStatus.OK)
  @Post('logout')
  async logout(@Req() req: any, @Headers() headers: any) {
    return CoreUtils.handleRequest(async () => {
      const refreshToken = req.headers.authorization?.replace('Bearer ', '');
      const userId = req.user?.id;

      if (refreshToken) {
        await this.authService.logout(refreshToken, userId);
      }
      return {
        message: this.i18n.t('message.success.logout'),
        data: null,
      };
    });
  }

  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Logout from All Devices' })
  @HttpCode(HttpStatus.OK)
  @Post('logout-all-devices')
  async logoutAllDevices(@Req() req: any, @Headers() headers: any) {
    return CoreUtils.handleRequest(async () => {
      const userId = req.user.id;

      await this.authService.logoutAllDevices(userId);
      return {
        message: this.i18n.t('message.success.logout_all_devices'),
        data: null,
      };
    });
  }

  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get Active Sessions' })
  @HttpCode(HttpStatus.OK)
  @Get('sessions')
  async getActiveSessions(@Req() req: any, @Headers() headers: any) {
    return CoreUtils.handleRequest(async () => {
      const userId = req.user.id;

      const sessions = await this.authService.getActiveSessions(userId);
      return {
        message: this.i18n.t('message.success.sessions_retrieved'),
        data: { sessions },
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Verify Account' })
  @HttpCode(HttpStatus.OK)
  @Post('verify-account')
  async verifyAccount(@Body() dto: VerifyAccountDto) {
    return CoreUtils.handleRequest(async () => {
      const { email, otp } = dto;
      await this.authService.verifyProfile(email, otp);

      return {
        message: this.i18n.t('message.success.account_verified'),
        data: null,
      };
    });
  }

  @Public()
  @Throttle({ default: { ttl: rateLimitConfig.auth.resendOtp.ttl, limit: rateLimitConfig.auth.resendOtp.limit } })
  @ApiOperation({ summary: 'Resend Verification OTP' })
  @HttpCode(HttpStatus.OK)
  @Post('resend-verification-otp')
  async resendOtp(@Body() dto: ResendVerificationOtpDto) {
    return CoreUtils.handleRequest(async () => {
      await this.authService.resendVerificationOtp(dto.email);
      return {
        message: this.i18n.t('message.success.verification_otp_resent'),
        data: null,
      };
    });
  }

  @Public()
  @Throttle({ default: { ttl: rateLimitConfig.auth.forgotPassword.ttl, limit: rateLimitConfig.auth.forgotPassword.limit } })
  @ApiOperation({ summary: 'Forgot password' })
  @HttpCode(HttpStatus.OK)
  @Post('forgot-password')
  async forgotPassword(@Body() dto: ForgotPasswordDto) {
    return CoreUtils.handleRequest(async () => {
      await this.authService.forgotPassword(dto.email);
      return {
        message: this.i18n.t('message.success.password_reset_otp_sent'),
        data: null,
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Reset password' })
  @HttpCode(HttpStatus.OK)
  @Post('reset-password')
  async resetPassword(@Body() dto: ResetPasswordDto) {
    return CoreUtils.handleRequest(async () => {
      const { email, otp, newPassword } = dto;
      await this.authService.resetPassword(email, otp, newPassword);
      return {
        message: this.i18n.t('message.success.reset_password'),
        data: null,
      };
    });
  }

  @Public()
  @Throttle({ default: { ttl: rateLimitConfig.auth.googleOAuth.ttl, limit: rateLimitConfig.auth.googleOAuth.limit } })
  @ApiOperation({ summary: 'Google Authentication with Access Token' })
  @ApiBody({ type: GoogleAuthTokenDto })
  @HttpCode(HttpStatus.OK)
  @Post('google/oauth/signin')
  async googleAuthWithToken(@Body() googleAuthTokenDto: GoogleAuthTokenDto) {
    return CoreUtils.handleRequest(async () => {
      const { accessToken } = googleAuthTokenDto;

      const result = await this.authService.handleGoogleAuthWithToken(accessToken);

      const data = {
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        profileInfo: this.classMapper.map(result.profile, Profile, ProfileDto),
        isNewUser: result.isNewUser
      };

      return {
        message: result.isNewUser
          ? this.i18n.t('message.success.google_signup')
          : this.i18n.t('message.success.google_signin'),
        data
      };
    });
  }
}
