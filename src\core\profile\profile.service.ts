import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { LoggerService } from '@common/logger/logger.service';
import { CustomerService } from '@core/customer/customer.service';
import { ProfileValidationService } from '@core/profile/profile.validation.service';
import { StaffService } from '@core/staff/staff.service';
import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { EntityManager, Repository } from 'typeorm';
import { Profile } from './entities/profile.entity';
import { EntityStatus } from '@common/base.entity';
import { EntityServiceStrategy } from '@common/entity.service.strategy';
import { PaginationDto } from '@common/pagination.dto';
import { PaginatedResponseDto } from '@common/paginated-response.dto';
import { Staff } from '@core/staff/entities/staff.entity';
import { Customer } from '@core/customer/entities/customer.entity';


interface ProfileTypeHandler {
  findEntity: (profileId: number) => Promise<Staff | Customer>;
  saveEntity: (entityId: number, entity: Staff | Customer) => Promise<Staff | Customer>;
}

@Injectable()
export class ProfileService implements EntityServiceStrategy<Profile> {
  /**
   * Map of profile types to their corresponding service methods
   */
  private readonly profileTypeHandlers = new Map<ProfileType, ProfileTypeHandler>([
    [ProfileType.STAFF, {
      findEntity: (profileId: number) => this.staffService.findStaffByProfileId(profileId),
      saveEntity: (entityId: number, entity: Staff) => this.staffService.modify(entityId, entity as Staff)
    }],
    [ProfileType.CLIENT, {
      findEntity: (profileId: number) => this.customerService.findCustomerByProfileId(profileId),
      saveEntity: (entityId: number, entity: Customer) => this.customerService.modify(entityId, entity as Customer)
    }],
  ]);

  constructor(
    private readonly logger: LoggerService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    @InjectRepository(Profile) private readonly profileRepository: Repository<Profile>,
    private readonly i18n: I18nService,
    private readonly profileValidation: ProfileValidationService,
    private readonly staffService: StaffService,
    private readonly customerService: CustomerService,
  ) {
    this.logger.setContext(ProfileService.name);
  }

  async create(data: Profile): Promise<Profile> {
    data.status = EntityStatus.INACTIVE;
    await this.profileValidation.validate(data, DatabaseAction.CREATE);
    return await this.entityManager.save(data);
  }

  async update(data: Profile): Promise<Profile> {
    await this.profileValidation.validate(data, DatabaseAction.UPDATE);
    return await this.entityManager.save(data);
  }

  async findByPk(id: number): Promise<Profile | null> {
    return await this.entityManager.findOneBy(Profile, { id });
  }

  async findByEmail(email: string) {
    return await this.entityManager.findOneBy(Profile, { email });
  }

  async modify(id: number, data: Profile): Promise<Profile> {
    await this.profileValidation.validate(data, DatabaseAction.UPDATE);
    return await this.entityManager.save(data);
  }

  //TODO: To be implemented soon
  table<R = Profile>(paginationDto: PaginationDto, ...args: Array<string | number>): Promise<PaginatedResponseDto<R>> {
    return Promise.resolve(undefined);
  }

  async activate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const profile: Profile = await this.entityManager.findOne(Profile, {
          where: { id },
        });
        profile.status = EntityStatus.ACTIVE;
        profile.profileStatus = ProfileStatus.ACTIVE;
        await this.entityManager.save(Profile, profile);
      }),
    );
  }

  async deactivate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const profile: Profile = await this.entityManager.findOne(Profile, {
          where: { id },
        });
        profile.status = EntityStatus.INACTIVE;
        profile.profileStatus = ProfileStatus.DEACTIVATED;
        await this.entityManager.save(Profile, profile);
      }),
    );
  }

  /**
   * Suspend a user account.
   * @param profileId
   */
  async suspendProfile(profileId: number) {
    const existingProfile = await this.isProfileExists(profileId);
    if (!existingProfile) {
      throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Profile' } }));
    }

    await this.updateProfileStatus(existingProfile, ProfileStatus.SUSPENDED);
  }

  /**
   * Ban a user account.
   * @param profileId
   */
  async banProfile(profileId: number) {
    const existingProfile = await this.isProfileExists(profileId);
    if (!existingProfile) {
      throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Profile' } }));
    }

    await this.updateProfileStatus(existingProfile, ProfileStatus.BANNED);
  }

  /**
   * Marks a profile as verified by email
   * @param email - The email of the profile to verify
   */
  async markProfileVerified(email: string) {
    const existingProfile = await this.entityManager.findOneBy(Profile, { email });
    if (!existingProfile) {
      throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Profile' } }));
    }

    const handler = this.getProfileTypeHandler(existingProfile.profileType);
    const entity = await handler.findEntity(existingProfile.id);
    this.activateEntity(entity);
    await handler.saveEntity(entity.id, entity);
  }

  /**
   * Check if an account exists.
   * @param profileId - The ID of the account to check.
   */
  isProfileExists(profileId: number) {
    return this.entityManager.findOne(Profile, {
      where: { id: profileId },
    });
  }

  async checkProfileEligibility(profile: Profile): Promise<boolean> {
    if (profile.verificationInfo.verified === false) {
      throw new ForbiddenException(this.i18n.t('errors.not_verified', { args: { entity: 'Profile' } }));
    }

    if (profile.profileStatus === ProfileStatus.SUSPENDED) {
      throw new ForbiddenException(this.i18n.t('errors.suspended', { args: { entity: 'Profile' } }));
    }

    if (profile.profileStatus === ProfileStatus.BANNED) {
      throw new ForbiddenException(this.i18n.t('errors.banned', { args: { entity: 'Profile' } }));
    }

    if (profile.profileStatus === ProfileStatus.DEACTIVATED) {
      throw new ForbiddenException(this.i18n.t('errors.deactivated', { args: { entity: 'Profile' } }));
    }

    return true;
  }

  async findProfileByEmail(email: string): Promise<Profile> {
    return await this.entityManager.findOne(Profile, { where: { email } });
  }

  /**
   * Generic method to update profile status for different profile types
   * @param profile - The profile to update
   * @param status - The new profile status
   */
  private async updateProfileStatus(profile: Profile, status: ProfileStatus): Promise<void> {
    const handler = this.getProfileTypeHandler(profile.profileType);
    const entity = await handler.findEntity(profile.id);

    entity.status = EntityStatus.INACTIVE;
    entity.profile.status = EntityStatus.INACTIVE;
    entity.profile.profileStatus = status;

    await handler.saveEntity(entity.id, entity);
  }

  /**
   * Activates an entity and its profile
   * @param entity - The entity (staff or customer) to activate
   */
  private activateEntity(entity: Staff | Customer): void {
    entity.status = EntityStatus.ACTIVE;
    entity.profile.status = EntityStatus.ACTIVE;
    entity.profile.profileStatus = ProfileStatus.ACTIVE;
    entity.profile.verificationInfo.verified = true;
  }

  /**
   * Gets the appropriate handler for a profile type
   * @param profileType - The profile type
   * @returns The handler for the profile type
   */
  private getProfileTypeHandler(profileType: ProfileType): ProfileTypeHandler {
    const handler = this.profileTypeHandlers.get(profileType);

    if (!handler) {
      throw new BadRequestException(`Unsupported profile type: ${profileType}`);
    }

    return handler;
  }
}
