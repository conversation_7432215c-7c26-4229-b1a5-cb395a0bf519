import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { EntityDto } from '@common/base.dto';

export class ItemDto extends EntityDto {
  @AutoMap()
  @ApiProperty({
    description: 'Name of the item',
    example: 'Organic Tomatoes',
    type: String,
  })
  name: string;

  @AutoMap()
  @ApiProperty({
    description: 'Description of the item',
    example: 'Fresh organic tomatoes from local farms',
    type: String,
    required: false,
  })
  description?: string;

  @AutoMap()
  @ApiProperty({
    description: 'Price of the item',
    example: 25.99,
    type: Number,
  })
  price: number;

  @AutoMap()
  @ApiProperty({
    description: 'Available units/quantity of the item',
    example: 100,
    type: Number,
  })
  unit: number;
}
