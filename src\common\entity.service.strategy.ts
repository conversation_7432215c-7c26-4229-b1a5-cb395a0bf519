import { PaginationDto } from '@common/pagination.dto';
import { PaginatedResponseDto } from '@common/paginated-response.dto';
import { IPaginationOptions, Pagination } from 'nestjs-typeorm-paginate';


export interface EntityServiceStrategy<T> {
  create(data: T): Promise<T>;
  modify(id: number, data: T): Promise<T>;
  findByPk(id: number): Promise<T | null>;
  activate(ids: number[]): Promise<void>;
  deactivate(ids: number[]): Promise<void>;
  table?<R = T>(
    paginationDto: PaginationDto,
    ...args: Array<string | number>
  ): Promise<PaginatedResponseDto<R>>;
  paginate?(options: IPaginationOptions, where?: any): Promise<Pagination<T>>
}
