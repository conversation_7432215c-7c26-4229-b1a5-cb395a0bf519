import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, CallHandler } from '@nestjs/common';
import { of } from 'rxjs';
import { ProfileContextInterceptor } from './profile-context.interceptor';
import { AbstractEntity } from '@common/base.entity';
import { LoggerService } from '@common/logger/logger.service';

describe('ProfileContextInterceptor', () => {
  let interceptor: ProfileContextInterceptor;
  let mockExecutionContext: ExecutionContext;
  let mockCallHandler: CallHandler;
  let mockLogger: jest.Mocked<LoggerService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProfileContextInterceptor,
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
      ],
    }).compile();

    interceptor = module.get<ProfileContextInterceptor>(ProfileContextInterceptor);
    mockLogger = module.get(LoggerService);

    // Mock ExecutionContext
    mockExecutionContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          user: null,
        }),
      }),
    } as ExecutionContext;

    // Mock CallHandler
    mockCallHandler = {
      handle: () => of('test'),
    } as CallHandler;

    // Mock AbstractEntity.setCurrentUser
    jest.spyOn(AbstractEntity, 'setCurrentUser').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(interceptor).toBeDefined();
  });

  it('should handle request without user profile', () => {
    const result = interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(mockLogger.warn).toHaveBeenCalledWith(
      'No user profile found in request, using SYSTEM context'
    );
    expect(AbstractEntity.setCurrentUser).toHaveBeenCalledWith('SYSTEM');
    expect(result).toBeDefined();
  });

  it('should handle request with user profile', () => {
    mockExecutionContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          user: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
          },
        }),
      }),
    } as ExecutionContext;

    const result = interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(mockLogger.log).toHaveBeenCalledWith(
      'Setting user context - User: John Doe, ID: 123'
    );
    expect(AbstractEntity.setCurrentUser).toHaveBeenCalledWith('John Doe');
    expect(result).toBeDefined();
  });

  it('should handle request with only firstName', () => {
    mockExecutionContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          user: {
            id: 123,
            firstName: 'John',
          },
        }),
      }),
    } as ExecutionContext;

    const result = interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(AbstractEntity.setCurrentUser).toHaveBeenCalledWith('John');
    expect(result).toBeDefined();
  });

  it('should handle request with only email', () => {
    mockExecutionContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          user: {
            id: 123,
            email: '<EMAIL>',
          },
        }),
      }),
    } as ExecutionContext;

    const result = interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(AbstractEntity.setCurrentUser).toHaveBeenCalledWith('<EMAIL>');
    expect(result).toBeDefined();
  });

  it('should handle request with no name fields', () => {
    mockExecutionContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          user: {
            id: 123,
          },
        }),
      }),
    } as ExecutionContext;

    const result = interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(AbstractEntity.setCurrentUser).toHaveBeenCalledWith('User-123');
    expect(result).toBeDefined();
  });

  it('should handle errors gracefully', () => {
    mockExecutionContext = {
      switchToHttp: () => {
        throw new Error('Test error');
      },
    } as ExecutionContext;

    const result = interceptor.intercept(mockExecutionContext, mockCallHandler);

    expect(mockLogger.error).toHaveBeenCalledWith(
      'Error in ProfileContextInterceptor: Test error',
      undefined
    );
    expect(AbstractEntity.setCurrentUser).toHaveBeenCalledWith('SYSTEM');
    expect(result).toBeDefined();
  });
});
