import { Injectable, NotFoundException } from '@nestjs/common';
import { LoggerService } from '@common/logger/logger.service';
import { Country } from './entities/country.entity';
import { I18nService } from 'nestjs-i18n';
import { InjectRepository } from '@nestjs/typeorm';
import { CountryValidationService } from './country.validation.service';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { EntityServiceStrategy } from '@common/entity.service.strategy';
import { EntityStatus } from '@common/base.entity';
import { RedisService } from '@database/redis/redis.service';
import { Repository } from 'typeorm';
import { IPaginationOptions, paginate, Pagination } from 'nestjs-typeorm-paginate';

@Injectable()
export class CountryService implements EntityServiceStrategy<Country> {
  constructor(
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectRepository(Country)
    private readonly countryRepository: Repository<Country>,
    private readonly countryValidator: CountryValidationService,
    private readonly redisService: RedisService
  ) {
    this.logger.setContext(CountryService.name);
  }

  async activate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const tenant = await this.countryRepository.findOne({ where: { id } });
        tenant.status = EntityStatus.ACTIVE;
        await this.countryRepository.save(tenant);
      }),
    );
  }

  async create(data: Country): Promise<Country> {
    await this.countryValidator.validate(data, DatabaseAction.CREATE);
    return await this.countryRepository.save(data);
  }

  async deactivate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const country = await this.countryRepository.findOne({ where: { id: id } });
        country.status = EntityStatus.INACTIVE;
        await this.countryRepository.save(country);
      }),
    );
  }

  async findByPk(id: number): Promise<Country | null> {
    return await this.countryRepository.findOne({ where: { id: id } });
  }

  async modify(id: number, data: Country): Promise<Country> {
    await this.countryValidator.validate(data, DatabaseAction.UPDATE);

    if (!(await this.findByPk(id)))
      throw new NotFoundException(this.i18n.t('errors.not_found', { args: { entity: 'Country' } }));
    return await this.countryRepository.save(data)
  }

  async findByCountryCode(countryCode: string): Promise<Country | null> {
    const cacheKey = `country:${countryCode}`;

    // 1. Try cache first
    const cachedTenant = await this.redisService.getCacheKey(cacheKey);
    if (cachedTenant) {
      return cachedTenant;
    }

    // 2. Fallback to DB
    const country = await this.countryRepository.findOne({
      select: {
        id: true,
        name: true,
        code: true,
        priceFactor: true,
        currencyCode: true,
        isBlocked: true,
      },
      where: { code: countryCode },
    });

    if (country) {
      // 3. Save in cache (e.g., 1 hour TTL)
      await this.redisService.setCacheKey(cacheKey, country as object, 3600);
    }

    return country;
  }

  /**
   * Paginate through roles with optional filtering criteria.
   * @param options
   * @param where
   */
  async paginate(options: IPaginationOptions, where?: any): Promise<Pagination<Country>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<Country>(this.countryRepository, options, {
        where,
        order: {
          createdAt: 'DESC'
        }
      });
    }
    return paginate<Country>(this.countryRepository, options);
  }
}
