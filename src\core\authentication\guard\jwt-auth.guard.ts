import {
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '@common/decorators/public.decorator';
import { I18nService } from "nestjs-i18n";

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector, private readonly i18n: I18nService) {
    super({
      passReqToCallback: true,
    });
  }

  canActivate(context: ExecutionContext) {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) return true

    return super.canActivate(context);
  }

  handleRequest(err: { message: any; }, user: any, info: { message: any; }) {
    if (err) {
      throw new UnauthorizedException(err.message);
    }
    if (info) {
      throw new UnauthorizedException(info.message);
    }
    if (!user) {
              throw new UnauthorizedException(this.i18n.t('errors.invalid_credentials', {args:{field: 'user'}}));
    }
    return user;
  }
}
