import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { EventName } from '@common/enumerations/event_name.enum';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { LoggerService } from '@common/logger/logger.service';
import { CoreConstants } from '@common/utils/core.constants';
import { CoreUtils } from '@common/utils/core.utils';
import { CustomerService } from '@core/customer/customer.service';
import { Customer } from '@core/customer/entities/customer.entity';
import { Profile } from '@core/profile/entities/profile.entity';
import { ProfileService } from '@core/profile/profile.service';
import { ProfileValidationService } from '@core/profile/profile.validation.service';
import { Staff } from '@core/staff/entities/staff.entity';
import { StaffService } from '@core/staff/staff.service';
import {
  BadRequestException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { JwtService } from '@nestjs/jwt';
import { InjectEntityManager } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { EntityManager } from 'typeorm';
import { EntityStatus } from '@common/base.entity';
import { Currency } from '@common/enumerations/currency.enum';
import { RedisService } from '@database/redis/redis.service';
import { IGoogleProfile } from '@core/authentication/strategy/google.strategy';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AuthenticationService {

  constructor(
    private readonly logger: LoggerService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly i18n: I18nService,
    private readonly profileService: ProfileService,
    private readonly customerService: CustomerService,
    private readonly staffService: StaffService,
    private readonly eventEmitter: EventEmitter2,
    private readonly profileValidationService: ProfileValidationService,
    private readonly redisService: RedisService,
    private readonly httpService: HttpService,
  ) {
    this.logger.setContext(AuthenticationService.name);
  }

  /**
   * Creates access and refresh tokens for a user.
   * @param userId - The ID of the user for whom the tokens are being created.
   * @param email - The email of the user for whom the tokens are being created.
   * @private
   */
  private async createTokens(
    userId: number,
    email: string,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    const payload = {
      sub: userId,
      email,
      type: 'access' // For access token
    };

    const refreshPayload = {
      sub: userId,
      email,
      type: 'refresh' // For refresh token
    };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        privateKey: this.configService.get<string>('keys.secret'),
        expiresIn: '15m',
      }),
      this.jwtService.signAsync(refreshPayload, {
        privateKey: this.configService.get<string>('keys.secret'),
        expiresIn: '7d',
      }),
    ]);
    return { accessToken, refreshToken };
  }

  /**
   * Map of profile types to their corresponding service methods
   */
  private readonly profileTypeHandlers = new Map([
    [ProfileType.STAFF, (profileId: number) => this.staffService.findStaffByProfileId(profileId)],
    [ProfileType.CLIENT, (profileId: number) => this.customerService.findCustomerByProfileId(profileId)],
  ]);

  /**
   * Handles a failed login attempt by incrementing the failed login count for the profile.
   * @param existingProfile - The profile of the user attempting to log in.
   */
  private async handleFailedLoginAttempt(existingProfile: Profile) {
    const handler = this.profileTypeHandlers.get(existingProfile.profileType);

    if (!handler) {
      throw new BadRequestException(`Unsupported profile type: ${existingProfile.profileType}`);
    }

    const entity = await handler(existingProfile.id);
    await this.processFailedLogin(entity);
  }

  /**
   * Processes the failed login logic for any entity type
   * @param entity - The entity (staff or customer) with profile information
   */
  private async processFailedLogin(entity: any) {
    entity.profile.authInfo.failedLoginCount += 1;

    if (entity.profile.authInfo.failedLoginCount >= CoreConstants.MAX_LOGIN_REATTEMPT) {
      entity.status = EntityStatus.INACTIVE;
      entity.profile.status = EntityStatus.INACTIVE;
      entity.profile.profileStatus = ProfileStatus.SUSPENDED;
      await this.entityManager.save(entity);
              throw new ForbiddenException(
          this.i18n.t('message.errors.suspended', { args: {entity: 'Profile'} })
        );
    }

    await this.entityManager.save(entity);
    const attemptsRemaining = CoreConstants.MAX_LOGIN_REATTEMPT - entity.profile.authInfo.failedLoginCount;
          throw new BadRequestException(
            this.i18n.t('message.errors.password_incorrect', { args: {attemptsRemaining} })
      );
  }

  /**
   * Signs in a user using their email or phone number and password.
   * @param email - The email or phone number of the user.
   * @param password - The password of the user.
   */
  async signin(
    email: string,
    password: string,
  ): Promise<{accessToken: string, refreshToken: string, profile: Profile}> {
    const existingProfile = await this.entityManager.findOne(Profile, {
      where: {email}
    });

    if (!existingProfile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: {entity: 'Profile' }}));
    }

    await this.profileService.checkProfileEligibility(existingProfile);

    const isPasswordMatch = CoreUtils.passwordMatch(password, existingProfile.authInfo.password);
    if (!isPasswordMatch) {
      await this.handleFailedLoginAttempt(existingProfile);
    }

    existingProfile.authInfo.failedLoginCount = null;
    await this.entityManager.save(existingProfile);

    const { accessToken, refreshToken } = await this.createTokens(
      existingProfile.id,
      existingProfile.email,
    );

    // Store token metadata in Redis for session management
    await this.storeTokenMetadata(accessToken, refreshToken, existingProfile.id);

    return {
      accessToken,
      refreshToken,
      profile: existingProfile,
    };
  }

  /**
   * Stores token metadata in Redis for session management.
   * @param accessToken - The access token.
   * @param refreshToken - The refresh token.
   * @param userId - The user ID.
   */
  private async storeTokenMetadata(
    accessToken: string,
    refreshToken: string,
    userId: number,
  ): Promise<void> {
    try {
      const tokenMetadata = {
        userId,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
      };

      // Store access token metadata
      await this.redisService.setCacheKey(
        `token:access:${accessToken}`,
        JSON.stringify(tokenMetadata),
        15 * 60 // 15 minutes
      );

      // Store refresh token metadata
      await this.redisService.setCacheKey(
        `token:refresh:${refreshToken}`,
        JSON.stringify(tokenMetadata),
        7 * 24 * 60 * 60 // 7 days
      );

      // Store user's active sessions
      const userSessionsKey = `sessions:${userId}`;
      const existingSessions = await this.redisService.getCacheKey(userSessionsKey);
      const sessions = existingSessions ? JSON.parse(existingSessions) : [];
      sessions.push({
        accessToken,
        refreshToken,
        createdAt: new Date().toISOString()
      });

      // Keep only the last 10 sessions per user
      if (sessions.length > 10) {
        sessions.splice(0, sessions.length - 10);
      }

      await this.redisService.setCacheKey(userSessionsKey, JSON.stringify(sessions), 7 * 24 * 60 * 60);

      this.logger.log(`Token metadata stored for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to store token metadata: ${error.message}`, error.stack);
      // Don't throw error to prevent blocking the login process
    }
  }

  /**
   * Signs up a new user by creating a profile and associated entities.
   * @param profile - The profile data to create.
   */
  async signup(profile: Profile) {
    await this.entityManager.transaction(async (manager) => {
      profile.authInfo.password = CoreUtils.hashPassword(profile.authInfo.password);
      profile.status = EntityStatus.INACTIVE;
      profile.profileStatus = ProfileStatus.INACTIVE;
      await this.profileValidationService.validate(profile, DatabaseAction.CREATE);
      const createdProfile = await manager.save(profile);

      const customer = new Customer();
      if (profile.profileType === ProfileType.CLIENT) {
        customer.status = EntityStatus.INACTIVE;
        customer.profile = createdProfile;
        customer.profile.verificationInfo = {
          verified: false,
          kycVerified: false,
        }
        //TODO: Handle currency based on user's location or preferences
        // TODO: Handle currency conversion if needed
        customer.wallet = {
          balance: 0,
          currency: Currency.DEFAULT,
          paystackCustomerId: null
        }
        await manager.save(customer);
      }

      const staff = new Staff();
      if (profile.profileType === ProfileType.STAFF) {
        staff.status = EntityStatus.INACTIVE;
        staff.profile = createdProfile;
        await manager.save(staff);
      }

      // Emit an event to send the OTP via email
      this.eventEmitter.emit(EventName.EMAIL_NOTIFICATION, {
        recipient: profile.email,
        subject: 'Account Verification',
        message: `Your Verification OTP is ${await this.assignOtpToProfile(profile.email)}.`,
      });
    });
  }

  /**
   * Verifies a profile using email and OTP code
   * @param email - The email of the profile to verify
   * @param code - The OTP code sent to the user's email
   */
  async verifyProfile(email: string, code: string) {
    const profile = await this.profileService.findByEmail(email);
    if (!profile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: {entity: 'Profile' } }));
    }

    if (profile.verificationInfo.verified) {
      throw new BadRequestException(this.i18n.t('message.errors.already_verified', { args: {entity: 'Profile' } }));
    }

    // Validate the OTP using the isValid method
    const isValidOtp = await this.isValid(email, code);
    if (!isValidOtp) {
      throw new BadRequestException(this.i18n.t('message.errors.otp_verify_fail', { args: {entity: 'OTP' } }));
    }

    // Mark the profile as verified
    await this.profileService.markProfileVerified(email);

    // Optionally, remove the OTP from cache after successful verification
    await this.redisService.delCacheKey(code);
  }


  /**
   * Resends the verification OTP to the user's email.
   * This method checks if the profile exists and if it is not already verified.
   * @param email
   */
  async resendVerificationOtp(email: string) {
    const profile = await this.profileService.findByEmail(email);
    if (!profile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: {entity: 'Profile' } }));
    }

    if (profile.verificationInfo.verified) {
      throw new BadRequestException(this.i18n.t('message.errors.already_verified', { args: {entity: 'Profile' } }));
    }

    this.eventEmitter.emit(EventName.EMAIL_NOTIFICATION, {
      recipient: email,
      subject: 'Account Verification',
      message: `Your account verification OTP is ${await this.assignOtpToProfile(email)}.`,
    });
  }

  /**
   * Send a link to reset password to user email.
   * @param email - The email of the user.
   */
  async forgotPassword(email: string) {
    const profile = await this.profileService.findByEmail(email);
    if (!profile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Email' } }));
    }

    await this.profileService.checkProfileEligibility(profile);

    this.eventEmitter.emit(EventName.EMAIL_NOTIFICATION, {
      recipient: email,
      subject: 'Password Reset',
      message: `Your password reset OTP is ${await this.assignOtpToProfile(email)}.`,
    });
  }

  /**
   * Resets the password for a user using their email, OTP, and new password.
   * @param email - The email of the user whose password is being reset.
   * @param otp - The One-Time Password (OTP) sent to the user's email for verification.
   * @param newPassword - The new password to set for the user.
   */
  async resetPassword(email: string, otp: string, newPassword: string) {
    const profile = await this.profileService.findByEmail(email);

    if (!profile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: {entity: 'Email'} }));
    }
    // Check if the profile is eligible for password reset
    await this.profileService.checkProfileEligibility(profile);

    // Validate the OTP
    const cachedEmail = await this.redisService.getCacheKey(otp);

    if (!cachedEmail) {
      throw new BadRequestException(this.i18n.t('message.errors.otp_expired'));
    }

    if (cachedEmail !== email) {
      throw new BadRequestException(this.i18n.t('errors.otp_verify_fail', { args: {entity: 'OTP'} }));
    }

    // Update the profile's password if the OTP is valid
    profile.authInfo.password = CoreUtils.hashPassword(newPassword);
    profile.authInfo.failedLoginCount = 0;
    profile.authInfo.passwordResetDate = new Date();

    await this.profileService.update(profile);
  }

  /**
   * Assigns a One-Time Password (OTP) to a profile for verification purposes.
   * This method generates a new OTP, caches it in Redis with a TTL, and returns the OTP code.
   * @param email - The email of the profile to which the OTP will be assigned.
   */
  async assignOtpToProfile(email:string): Promise<string>{
    // Generate a new OTP for the profile.
    const code = CoreUtils.generateOtp(CoreConstants.OTP_LENGTH)
    //Cache the OTP in Redis with a TTL
    await this.redisService.setCacheKey(code, email, CoreConstants.OTP_TTL * 1000);
    return code;
  }

  /**
   * Checks if the provided OTP code is valid for the given email.
   * This method retrieves the cached OTP from Redis and compares it with the provided email.
   * @param email
   * @param code
   */
  async isValid(email:string, code: string): Promise<boolean>{
    const existingRecord = await this.redisService.getCacheKey(code);
    return !(!existingRecord || existingRecord !== email);

  }

  /**
   * Refreshes the access token using a valid refresh token.
   * @param userId - The user ID from the refresh token payload
   * @param email - The email from the refresh token payload
   * @param refreshToken - The current refresh token
   */
  async refreshTokens(
    userId: number,
    email: string,
    refreshToken: string
  ): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // Verify the refresh token is valid and not blacklisted
      const isTokenValid = await this.redisService.getCacheKey(`blacklist:${refreshToken}`);
      if (isTokenValid) {
        throw new ForbiddenException('Refresh token has been revoked');
      }

      // Verify token metadata matches
      const tokenMetadata = await this.redisService.getCacheKey(`token:refresh:${refreshToken}`);
      if (!tokenMetadata) {
        throw new ForbiddenException('Invalid refresh token');
      }

      const metadata = JSON.parse(tokenMetadata);
      if (metadata.userId !== userId) {
        throw new ForbiddenException('Token metadata mismatch');
      }

      // Get the user profile to ensure they still exist and are eligible
      const profile = await this.profileService.findProfileByEmail(email);
      await this.profileService.checkProfileEligibility(profile);

      // Create new tokens
      const newTokens = await this.createTokens(userId, email);

      // Blacklist the old refresh token
      await this.redisService.setCacheKey(`blacklist:${refreshToken}`, 'revoked', 7 * 24 * 60 * 60);

      // Store new token metadata
      await this.storeTokenMetadata(
        newTokens.accessToken,
        newTokens.refreshToken,
        userId as number,
      );

      this.logger.log(`Tokens refreshed for user ${email}`);
      return newTokens;
    } catch (error) {
      this.logger.error(`Failed to refresh tokens for user ${email}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Logs out a user by blacklisting their refresh token and clearing session data.
   * @param refreshToken - The refresh token to blacklist
   * @param userId - The user ID
   */
  async logout(refreshToken: string, userId?: number): Promise<void> {
    try {
      // Blacklist the refresh token
      await this.redisService.setCacheKey(`blacklist:${refreshToken}`, 'revoked', 7 * 24 * 60 * 60);

      // Clear token metadata
      await this.redisService.delCacheKey(`token:refresh:${refreshToken}`);

      // Remove from user sessions if userId is provided
      if (userId) {
        const userSessionsKey = `sessions:${userId}`;
        const existingSessions = await this.redisService.getCacheKey(userSessionsKey);
        if (existingSessions) {
          const sessions = JSON.parse(existingSessions);
          const updatedSessions = sessions.filter((session: any) => session.refreshToken !== refreshToken);
          await this.redisService.setCacheKey(userSessionsKey, JSON.stringify(updatedSessions), 7 * 24 * 60 * 60);
        }
      }

      this.logger.log('User logged out successfully');
    } catch (error) {
      this.logger.error(`Failed to logout user: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Logs out a user from all devices by clearing all their sessions.
   * @param userId - The user ID to logout from all devices
   */
  async logoutAllDevices(userId: number): Promise<void> {
    try {
      const userSessionsKey = `sessions:${userId}`;
      const existingSessions = await this.redisService.getCacheKey(userSessionsKey);

      if (existingSessions) {
        const sessions = JSON.parse(existingSessions);

        // Blacklist all refresh tokens
        for (const session of sessions) {
          await this.redisService.setCacheKey(`blacklist:${session.refreshToken}`, 'revoked', 7 * 24 * 60 * 60);
          await this.redisService.delCacheKey(`token:refresh:${session.refreshToken}`);
          await this.redisService.delCacheKey(`token:access:${session.accessToken}`);
        }

        // Clear all sessions
        await this.redisService.delCacheKey(userSessionsKey);
      }

      this.logger.log(`User ${userId} logged out from all devices`);
    } catch (error) {
      this.logger.error(`Failed to logout user from all devices: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gets active sessions for a user.
   * @param userId - The user ID
   */
  async getActiveSessions(userId: number): Promise<any[]> {
    try {
      const userSessionsKey = `sessions:${userId}`;
      const existingSessions = await this.redisService.getCacheKey(userSessionsKey);

      if (existingSessions) {
        return JSON.parse(existingSessions);
      }

      return [];
    } catch (error) {
      this.logger.error(`Failed to get active sessions: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Handle Google authentication for both new and existing users
   */
  async handleGoogleAuth(
    googleProfile: IGoogleProfile
  ): Promise<{ accessToken: string; refreshToken: string; profile: Profile; isNewUser: boolean }> {
    try {
      const { firstName, lastName, email, picture } = googleProfile;

      // Enhanced validation
      if (!email?.trim()) {
        throw new BadRequestException(this.i18n.t('message.errors.google_email_required'));
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new BadRequestException(this.i18n.t('message.errors.google_email_invalid'));
      }

      // Validate and sanitize picture URL
      let sanitizedPicture = '';
      if (picture && this.isValidUrl(picture)) {
        sanitizedPicture = picture;
      } else if (picture) {
        this.logger.warn(`Invalid picture URL from Google: ${picture}`);
      }

      // Check if user already exists
      let existingProfile = await this.profileService.findByEmail(email);

      if (existingProfile) {
        // User exists - sign in
        this.logger.log(`Google OAuth signin for existing user: ${email}`);

        // Update profile with latest Google data (only if new data is provided)
        if (firstName && firstName !== existingProfile.firstName) {
          existingProfile.firstName = firstName;
        }
        if (lastName && lastName !== existingProfile.lastName) {
          existingProfile.lastName = lastName;
        }

        // Update auth info with Google data
        if (!existingProfile.authInfo) {
          existingProfile.authInfo = {};
        }
        existingProfile.authInfo.lastGoogleSignIn = new Date();

        // Update profile picture if a valid one is provided
        if (sanitizedPicture && (!existingProfile.settings?.profilePicture || existingProfile.settings.profilePicture !== sanitizedPicture)) {
          if (!existingProfile.settings) {
            existingProfile.settings = {};
          }
          existingProfile.settings.profilePicture = sanitizedPicture;
        }

        // Save updated profile
        await this.entityManager.save(Profile, existingProfile);

        // Create tokens
        const tokens = await this.createTokens(
          existingProfile.id,
          existingProfile.email,
        );

        // Store token metadata
        await this.storeTokenMetadata(
          tokens.accessToken,
          tokens.refreshToken,
          existingProfile.id,
        );

        return {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          profile: existingProfile,
          isNewUser: false
        };
      } else {
        // User doesn't exist - sign up
        this.logger.log(`Google OAuth signup for new user: ${email}`);

        // Create new profile
        const newProfile = new Profile();
        newProfile.email = email;
        newProfile.firstName = firstName || '';
        newProfile.lastName = lastName || '';
        newProfile.phoneNumber = ''; // Will be updated later
        newProfile.profileStatus = ProfileStatus.ACTIVE; // Auto-activate Google users
        newProfile.profileType = ProfileType.CLIENT;
        newProfile.authInfo = {
          lastLogin: new Date(),
          lastGoogleSignIn: new Date(),
        };
        newProfile.settings = {
          profilePicture: sanitizedPicture,
        };
        newProfile.verificationInfo = {
          verified: true,
          verificationDate: new Date(),
        };

        // Save the new profile
        const savedProfile = await this.entityManager.save(Profile, newProfile);

        // Create tokens
        const tokens = await this.createTokens(
          savedProfile.id,
          savedProfile.email
        );

        // Store token metadata
        await this.storeTokenMetadata(
          tokens.accessToken,
          tokens.refreshToken,
          savedProfile.id
        );

        return {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          profile: savedProfile,
          isNewUser: true
        };
      }
    } catch (error) {
      this.logger.error(`Google OAuth authentication failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Verify Google access token by calling Google's API
   * This method actually validates the token with Google and retrieves user information
   */
  async verifyGoogleToken(accessToken: string): Promise<IGoogleProfile> {
    if (!accessToken?.trim()) {
      throw new BadRequestException(this.i18n.t('message.errors.google_token_required'));
    }

    try {
      const userInfoUrl = `https://www.googleapis.com/oauth2/v3/userinfo?access_token=${encodeURIComponent(accessToken)}`;

      const response = await firstValueFrom(
        this.httpService.get(userInfoUrl)
      );

      const { given_name, family_name, email, picture, sub } = response.data;

      // Validate required fields
      if (!email?.trim()) {
        throw new BadRequestException(this.i18n.t('message.errors.google_email_required'));
      }

      if (!sub) {
        throw new BadRequestException(this.i18n.t('message.errors.google_user_id_required'));
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new BadRequestException(this.i18n.t('message.errors.google_email_invalid'));
      }

      return {
        id: sub,
        email: email.trim(),
        firstName: given_name?.trim() || '',
        lastName: family_name?.trim() || '',
        picture: picture?.trim() || '',
      };
    } catch (error) {
      if (error.response?.status === HttpStatus.UNAUTHORIZED) {
        throw new UnauthorizedException(this.i18n.t('message.errors.google_token_invalid'));
      }

      if (error.response?.status === HttpStatus.BAD_REQUEST) {
        throw new BadRequestException(this.i18n.t('message.errors.google_token_format'));
      }

      if (error.response?.status === HttpStatus.TOO_MANY_REQUESTS) {
        throw new BadRequestException(this.i18n.t('message.errors.google_rate_limit'));
      }

      this.logger.error(`Google token verification failed: ${error.message}`, error.stack);
      
      // Don't expose internal errors to the client
      if (error instanceof BadRequestException || error instanceof UnauthorizedException) {
        throw error;
      }
      
      throw new BadRequestException(this.i18n.t('message.errors.google_verification_failed'));
    }
  }

  /**
   * Handle Google authentication with token verification
   * This method verifies the token with Google API before processing
   */
  async handleGoogleAuthWithToken(accessToken: string): Promise<{ accessToken: string; refreshToken: string; profile: Profile; isNewUser: boolean }> {
    // Validate input
    if (!accessToken?.trim()) {
      throw new BadRequestException(this.i18n.t('message.errors.google_token_required'));
    }

    try {
      // First verify the token with Google API
      const googleProfile = await this.verifyGoogleToken(accessToken);

      // Then proceed with the existing logic
      return this.handleGoogleAuth(googleProfile);
    } catch (error) {
      // Re-throw specific exceptions to preserve error context
      if (error instanceof BadRequestException || error instanceof UnauthorizedException) {
        throw error;
      }
      
      this.logger.error(`Google OAuth authentication with token failed: ${error.message}`, error.stack);
      throw new BadRequestException(this.i18n.t('message.errors.google_authentication_failed'));
    }
  }

  /**
   * Validate if a string is a valid URL
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}
