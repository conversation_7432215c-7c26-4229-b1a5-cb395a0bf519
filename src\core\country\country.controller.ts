import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { LoggerService } from '@common/logger/logger.service';
import { CoreUtils } from '@common/utils/core.utils';
import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { I18nService } from 'nestjs-i18n';
import { ActivateCountryDto } from './dto/activate-country.dto';
import { DeactivateCountryDto } from './dto/deactivate-country.dto';
import { CreateCountryDto } from './dto/create-country.dto';
import { UpdateCountryDto } from './dto/update-country.dto';
import { CountryDto } from './dto/country.dto';
import { Country } from './entities/country.entity';
import { CountryService } from './country.service';
import { PaginationDto } from '@common/pagination.dto';
import { Like } from 'typeorm';
import { Pagination } from 'nestjs-typeorm-paginate';

//TODO: Only global user is authorized to use these endpoints.
@ApiTags('Country Management Endpoints')
@Controller({
  path: 'country',
  version: '1',
})
export class CountryController {
  constructor(
    private readonly countryService: CountryService,
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(CountryController.name);
  }

  @ApiOperation({ summary: 'Create country' })
  @ApiBody({ type: CreateCountryDto })
  @Post('/new')
  async createCountry(@Body() dto: CreateCountryDto) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.classMapper.mapAsync(dto, CreateCountryDto, Country);

      await this.countryService.create(data);

      return {
        message: this.i18n.t('message.success.created', { args: { entity: 'Country' } }),
        data: null
      };
    });
  }

  @ApiOperation({ summary: 'Update country' })
  @ApiBody({ type: UpdateCountryDto })
  @Put('modify/:countryId')
  async updateTenant(@Param('countryId', ParseIntPipe) countryId: number, @Body() dto: UpdateCountryDto) {
    return CoreUtils.handleRequest(async () => {
      const existingCountry = await this.countryService.findByPk(countryId);
      await this.classMapper.mutateAsync(dto, existingCountry, UpdateCountryDto, Country);
      await this.countryService.modify(countryId, existingCountry);
      return {
        message: this.i18n.t('message.success.updated', { args: { entity: 'Country' } }),
        data: null
      };
    });
  }

  @ApiOperation({ summary: 'Get country details' })
  @Get('/:countryId')
  async getCountryDetails(@Param('countryId', ParseIntPipe) countryId: number) {
    return CoreUtils.handleRequest(async () => {
      const existingCountry: Country = await this.countryService.findByPk(countryId);
      const data = await this.classMapper.mapAsync(existingCountry, Country, CountryDto);
      return {
        message: this.i18n.t('message.success.retrieved', { args: { entity: 'Country' } }),
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Activate country(s)' })
  @ApiBody({ type: ActivateCountryDto })
  @Patch('activate')
  async activateTenants(@Body() body: ActivateCountryDto) {
    return CoreUtils.handleRequest(async () => {
      await this.countryService.activate(body.ids);
      return {
        message: this.i18n.t('message.success.activated', { args: { entity: 'Country' } }),
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Deactivate country(s)' })
  @ApiBody({ type: DeactivateCountryDto })
  @Patch('deactivate')
  async deactivateTenants(@Body() body: DeactivateCountryDto) {
    return CoreUtils.handleRequest(async () => {
      await this.countryService.deactivate(body.ids);
      return {
        message: this.i18n.t('message.success.deactivated', { args: { entity: 'Country' } }),
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Get countries in table format' })
  @HttpCode(HttpStatus.OK)
  // @ApiQuery({ type: PaginationDto })
  @Get()
  async table(@Query() paginationDto: PaginationDto) {
    return CoreUtils.handleRequest(async () => {
      let { page = 1, limit = 10, search, filter } = paginationDto;

      limit = limit > 100 ? 100 : limit; // limit the pagination to 100
      const where = {};
      if (search) {
        where['name'] = Like(`%${search}%`);
      }
      if (filter) {
        where['status'] = filter;
      }

      const pagination = await this.countryService.paginate(
        {
          page,
          limit,
          route: `/api/country`,
        },
        where,
      );

      const result = await this.classMapper.mapArrayAsync(
        pagination.items,
        Country,
        CountryDto,
      );

      return {
        message: this.i18n.t('message.success.retrieved', { args: { entity: 'Country paginated list' } }),
        data: new Pagination(result, pagination.meta, pagination.links),
      };
    });
  }
}
