import { Test, TestingModule } from '@nestjs/testing';
import { ItemController } from './item.controller';
import { ItemService } from './item.service';
import { Mapper } from '@automapper/core';
import { I18nService } from 'nestjs-i18n';
import { LoggerService } from '@common/logger/logger.service';

describe('ItemController', () => {
  let controller: ItemController;
  let service: ItemService;

  const mockItemService = {
    findAllItems: jest.fn(),
    findOneItem: jest.fn(),
    createFromDto: jest.fn(),
    modify: jest.fn(),
    activate: jest.fn(),
    deactivate: jest.fn(),
    remove: jest.fn(),
  };

  const mockMapper = {
    mapArrayAsync: jest.fn(),
    mapAsync: jest.fn(),
  };

  const mockI18nService = {
    t: jest.fn().mockReturnValue('Success message'),
  };

  const mockLoggerService = {
    setContext: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ItemController],
      providers: [
        {
          provide: ItemService,
          useValue: mockItemService,
        },
        {
          provide: 'Mapper',
          useValue: mockMapper,
        },
        {
          provide: I18nService,
          useValue: mockI18nService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    controller = module.get<ItemController>(ItemController);
    service = module.get<ItemService>(ItemService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllItems', () => {
    it('should return paginated items', async () => {
      const mockItems = [{ id: 1, name: 'Test Item' }];
      const mockPagination = {
        items: mockItems,
        meta: { totalItems: 1 },
        links: {},
      };

      mockItemService.findAllItems.mockResolvedValue(mockPagination);
      mockMapper.mapArrayAsync.mockResolvedValue(mockItems);

      const result = await controller.getAllItems(1, 10, '', '', '/items');

      expect(service.findAllItems).toHaveBeenCalledWith({ page: 1, limit: 10, search: '', filter: '' }, '/items');
      expect(result).toBeDefined();
    });
  });

  describe('getOneItem', () => {
    it('should return a single item', async () => {
      const mockItem = { id: 1, name: 'Test Item' };
      mockItemService.findOneItem.mockResolvedValue(mockItem);
      mockMapper.mapAsync.mockResolvedValue(mockItem);

      const result = await controller.getOneItem(1);

      expect(service.findOneItem).toHaveBeenCalledWith(1);
      expect(result).toBeDefined();
    });
  });

  describe('createItem', () => {
    it('should create a new item', async () => {
      const createDto = { productId: 1, name: 'New Item', price: 10.99, unit: 50 };
      const mockItem = { id: 1, ...createDto };

      mockItemService.createFromDto.mockResolvedValue(mockItem);
      mockMapper.mapAsync.mockResolvedValue(mockItem);

      const result = await controller.createItem(createDto);

      expect(service.createFromDto).toHaveBeenCalledWith(createDto);
      expect(result).toBeDefined();
    });
  });

  describe('activateItems', () => {
    it('should activate items', async () => {
      const activateDto = { ids: [1, 2] };
      mockItemService.activate.mockResolvedValue(undefined);

      const result = await controller.activateItems(activateDto);

      expect(service.activate).toHaveBeenCalledWith([1, 2]);
      expect(result).toBeDefined();
    });
  });

  describe('deactivateItems', () => {
    it('should deactivate items', async () => {
      const deactivateDto = { ids: [1, 2] };
      mockItemService.deactivate.mockResolvedValue(undefined);

      const result = await controller.deactivateItems(deactivateDto);

      expect(service.deactivate).toHaveBeenCalledWith([1, 2]);
      expect(result).toBeDefined();
    });
  });

  describe('deleteItem', () => {
    it('should delete an item', async () => {
      mockItemService.remove.mockResolvedValue(undefined);

      const result = await controller.deleteItem(1);

      expect(service.remove).toHaveBeenCalledWith(1);
      expect(result).toBeDefined();
    });
  });
});
